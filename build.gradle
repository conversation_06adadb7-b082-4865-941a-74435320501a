plugins {
    id 'java'
    id 'application'
    id 'maven-publish'
}

group = 'com.fabricobfuscator'
version = '1.0.0'
sourceCompatibility = '17'

repositories {
    mavenCentral()
    maven {
        name = 'Fabric'
        url = 'https://maven.fabricmc.net/'
    }
    maven {
        name = 'Mojang'
        url = 'https://libraries.minecraft.net/'
    }
}

dependencies {
    // ASM для работы с байт-кодом
    implementation 'org.ow2.asm:asm:9.6'
    implementation 'org.ow2.asm:asm-tree:9.6'
    implementation 'org.ow2.asm:asm-commons:9.6'
    implementation 'org.ow2.asm:asm-util:9.6'
    
    // Для работы с JAR файлами
    implementation 'net.lingala.zip4j:zip4j:2.11.5'
    
    // JSON для конфигурации
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Криптография
    implementation 'org.bouncycastle:bcprov-jdk18on:1.77'
    
    // Логирование
    implementation 'org.slf4j:slf4j-api:2.0.9'
    implementation 'ch.qos.logback:logback-classic:1.4.14'
    
    // Fabric mappings (для понимания структуры модов)
    implementation 'net.fabricmc:tiny-mappings-parser:0.3.0+build.17'
    
    // Тестирование
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.1'
    testImplementation 'org.mockito:mockito-core:5.8.0'
}

application {
    mainClass = 'com.fabricobfuscator.FabricObfuscator'
}

jar {
    manifest {
        attributes(
            'Main-Class': 'com.fabricobfuscator.FabricObfuscator',
            'Implementation-Title': 'Fabric Mod Obfuscator',
            'Implementation-Version': project.version
        )
    }
    
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

test {
    useJUnitPlatform()
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
    options.compilerArgs += ['-parameters']
}
