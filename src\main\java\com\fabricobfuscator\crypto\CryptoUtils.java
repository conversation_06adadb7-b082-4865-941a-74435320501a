package com.fabricobfuscator.crypto;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Криптографические утилиты для обфускатора Fix85
 */
public class CryptoUtils {
    private static final String AES_ALGORITHM = "AES";
    private static final String SHA_ALGORITHM = "SHA-256";
    private static final SecureRandom random = new SecureRandom();
    
    /**
     * Шифрует данные с использованием AES
     */
    public static byte[] encrypt(byte[] data, byte[] key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(padKey(key), AES_ALGORITHM);
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        return cipher.doFinal(data);
    }
    
    /**
     * Расшифровывает данные с использованием AES
     */
    public static byte[] decrypt(byte[] encryptedData, byte[] key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(padKey(key), AES_ALGORITHM);
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        return cipher.doFinal(encryptedData);
    }
    
    /**
     * Вычисляет SHA-256 хеш
     */
    public static byte[] sha256(byte[] data) {
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA_ALGORITHM);
            return digest.digest(data);
        } catch (Exception e) {
            throw new RuntimeException("Ошибка вычисления хеша", e);
        }
    }
    
    /**
     * Генерирует случайный ключ
     */
    public static byte[] generateRandomKey(int length) {
        byte[] key = new byte[length];
        random.nextBytes(key);
        return key;
    }
    
    /**
     * Дополняет ключ до нужной длины
     */
    private static byte[] padKey(byte[] key) {
        byte[] paddedKey = new byte[16]; // AES-128
        System.arraycopy(key, 0, paddedKey, 0, Math.min(key.length, paddedKey.length));
        return paddedKey;
    }
    
    /**
     * XOR шифрование (простое, но эффективное для обфускации)
     */
    public static byte[] xorEncrypt(byte[] data, byte[] key) {
        byte[] result = new byte[data.length];
        for (int i = 0; i < data.length; i++) {
            result[i] = (byte) (data[i] ^ key[i % key.length]);
        }
        return result;
    }
    
    /**
     * Кодирует в Base64
     */
    public static String encodeBase64(byte[] data) {
        return Base64.getEncoder().encodeToString(data);
    }
    
    /**
     * Декодирует из Base64
     */
    public static byte[] decodeBase64(String data) {
        return Base64.getDecoder().decode(data);
    }
}
