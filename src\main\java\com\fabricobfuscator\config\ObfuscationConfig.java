package com.fabricobfuscator.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * Конфигурация обфускатора Fix85
 */
public class ObfuscationConfig {
    // Уровни обфускации имен (1-5)
    private int nameObfuscationLevel = 5;
    
    // Уровни обфускации строк (1-4)
    private int stringObfuscationLevel = 4;
    
    // Включить защиту от отладки
    private boolean antiDebugProtection = true;
    
    // Включить контрольные суммы
    private boolean checksumProtection = true;
    
    // Включить ватермарки Fix85
    private boolean enableWatermarks = true;
    
    // Исключения для обфускации
    private List<String> excludeClasses = Arrays.asList(
        "net/minecraft/**",
        "net/fabricmc/**",
        "org/spongepowered/**",
        "**/*Mixin*",
        "**/*mixin*"
    );
    
    private List<String> excludeMethods = Arrays.asList(
        "main",
        "init",
        "onInitialize*",
        "on*"
    );
    
    // Настройки ватермарок
    private WatermarkConfig watermarkConfig = new WatermarkConfig();
    
    // Настройки защиты
    private ProtectionConfig protectionConfig = new ProtectionConfig();
    
    public static class WatermarkConfig {
        private String author = "Fix85";
        private boolean embedInClassNames = true;
        private boolean embedInMethodNames = true;
        private boolean embedInConstants = true;
        private boolean embedTimestamp = true;
        private int watermarkDensity = 3; // Количество ватермарок на класс
        
        // Getters and setters
        public String getAuthor() { return author; }
        public void setAuthor(String author) { this.author = author; }
        
        public boolean isEmbedInClassNames() { return embedInClassNames; }
        public void setEmbedInClassNames(boolean embedInClassNames) { this.embedInClassNames = embedInClassNames; }
        
        public boolean isEmbedInMethodNames() { return embedInMethodNames; }
        public void setEmbedInMethodNames(boolean embedInMethodNames) { this.embedInMethodNames = embedInMethodNames; }
        
        public boolean isEmbedInConstants() { return embedInConstants; }
        public void setEmbedInConstants(boolean embedInConstants) { this.embedInConstants = embedInConstants; }
        
        public boolean isEmbedTimestamp() { return embedTimestamp; }
        public void setEmbedTimestamp(boolean embedTimestamp) { this.embedTimestamp = embedTimestamp; }
        
        public int getWatermarkDensity() { return watermarkDensity; }
        public void setWatermarkDensity(int watermarkDensity) { this.watermarkDensity = watermarkDensity; }
    }
    
    public static class ProtectionConfig {
        private boolean antiDecompilation = true;
        private boolean antiReflection = true;
        private boolean integrityChecks = true;
        private boolean runtimeValidation = true;
        private int protectionLevel = 5; // 1-5
        
        // Getters and setters
        public boolean isAntiDecompilation() { return antiDecompilation; }
        public void setAntiDecompilation(boolean antiDecompilation) { this.antiDecompilation = antiDecompilation; }
        
        public boolean isAntiReflection() { return antiReflection; }
        public void setAntiReflection(boolean antiReflection) { this.antiReflection = antiReflection; }
        
        public boolean isIntegrityChecks() { return integrityChecks; }
        public void setIntegrityChecks(boolean integrityChecks) { this.integrityChecks = integrityChecks; }
        
        public boolean isRuntimeValidation() { return runtimeValidation; }
        public void setRuntimeValidation(boolean runtimeValidation) { this.runtimeValidation = runtimeValidation; }
        
        public int getProtectionLevel() { return protectionLevel; }
        public void setProtectionLevel(int protectionLevel) { this.protectionLevel = protectionLevel; }
    }
    
    /**
     * Загружает конфигурацию из файла
     */
    public static ObfuscationConfig load(String configPath) {
        try {
            if (Files.exists(Paths.get(configPath))) {
                Gson gson = new Gson();
                try (FileReader reader = new FileReader(configPath)) {
                    return gson.fromJson(reader, ObfuscationConfig.class);
                }
            } else {
                // Создаем конфигурацию по умолчанию
                ObfuscationConfig defaultConfig = createDefault();
                defaultConfig.save(configPath);
                return defaultConfig;
            }
        } catch (IOException e) {
            System.err.println("Ошибка загрузки конфигурации: " + e.getMessage());
            return createDefault();
        }
    }
    
    /**
     * Сохраняет конфигурацию в файл
     */
    public void save(String configPath) throws IOException {
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        try (FileWriter writer = new FileWriter(configPath)) {
            gson.toJson(this, writer);
        }
    }
    
    /**
     * Создает конфигурацию по умолчанию с максимальной защитой Fix85
     */
    public static ObfuscationConfig createDefault() {
        ObfuscationConfig config = new ObfuscationConfig();
        
        // Максимальные уровни защиты
        config.nameObfuscationLevel = 5;
        config.stringObfuscationLevel = 4;
        config.antiDebugProtection = true;
        config.checksumProtection = true;
        config.enableWatermarks = true;
        
        // Настройки ватермарок Fix85
        config.watermarkConfig.author = "Fix85";
        config.watermarkConfig.embedInClassNames = true;
        config.watermarkConfig.embedInMethodNames = true;
        config.watermarkConfig.embedInConstants = true;
        config.watermarkConfig.embedTimestamp = true;
        config.watermarkConfig.watermarkDensity = 5;
        
        // Максимальная защита
        config.protectionConfig.antiDecompilation = true;
        config.protectionConfig.antiReflection = true;
        config.protectionConfig.integrityChecks = true;
        config.protectionConfig.runtimeValidation = true;
        config.protectionConfig.protectionLevel = 5;
        
        return config;
    }
    
    // Getters and setters
    public int getNameObfuscationLevel() { return nameObfuscationLevel; }
    public void setNameObfuscationLevel(int nameObfuscationLevel) { this.nameObfuscationLevel = nameObfuscationLevel; }
    
    public int getStringObfuscationLevel() { return stringObfuscationLevel; }
    public void setStringObfuscationLevel(int stringObfuscationLevel) { this.stringObfuscationLevel = stringObfuscationLevel; }
    
    public boolean isAntiDebugProtection() { return antiDebugProtection; }
    public void setAntiDebugProtection(boolean antiDebugProtection) { this.antiDebugProtection = antiDebugProtection; }
    
    public boolean isChecksumProtection() { return checksumProtection; }
    public void setChecksumProtection(boolean checksumProtection) { this.checksumProtection = checksumProtection; }
    
    public boolean isEnableWatermarks() { return enableWatermarks; }
    public void setEnableWatermarks(boolean enableWatermarks) { this.enableWatermarks = enableWatermarks; }
    
    public List<String> getExcludeClasses() { return excludeClasses; }
    public void setExcludeClasses(List<String> excludeClasses) { this.excludeClasses = excludeClasses; }
    
    public List<String> getExcludeMethods() { return excludeMethods; }
    public void setExcludeMethods(List<String> excludeMethods) { this.excludeMethods = excludeMethods; }
    
    public WatermarkConfig getWatermarkConfig() { return watermarkConfig; }
    public void setWatermarkConfig(WatermarkConfig watermarkConfig) { this.watermarkConfig = watermarkConfig; }
    
    public ProtectionConfig getProtectionConfig() { return protectionConfig; }
    public void setProtectionConfig(ProtectionConfig protectionConfig) { this.protectionConfig = protectionConfig; }
    
    /**
     * Проверяет, нужно ли исключить класс из обфускации
     */
    public boolean shouldExcludeClass(String className) {
        for (String pattern : excludeClasses) {
            if (matchesPattern(className, pattern)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Проверяет, нужно ли исключить метод из обфускации
     */
    public boolean shouldExcludeMethod(String methodName) {
        for (String pattern : excludeMethods) {
            if (matchesPattern(methodName, pattern)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Проверяет соответствие паттерну (поддерживает * и **)
     */
    private boolean matchesPattern(String text, String pattern) {
        if (pattern.equals("**")) {
            return true;
        }
        
        if (pattern.contains("**")) {
            String[] parts = pattern.split("\\*\\*");
            if (parts.length == 2) {
                return text.startsWith(parts[0]) && text.endsWith(parts[1]);
            }
        }
        
        if (pattern.contains("*")) {
            String regex = pattern.replace("*", ".*");
            return text.matches(regex);
        }
        
        return text.equals(pattern);
    }
    
    @Override
    public String toString() {
        return String.format(
            "ObfuscationConfig{nameLevel=%d, stringLevel=%d, antiDebug=%s, watermarks=%s, author=%s}",
            nameObfuscationLevel, stringObfuscationLevel, antiDebugProtection, 
            enableWatermarks, watermarkConfig.author
        );
    }
}
