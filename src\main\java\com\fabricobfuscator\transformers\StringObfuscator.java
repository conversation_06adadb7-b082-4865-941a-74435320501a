package com.fabricobfuscator.transformers;

import com.fabricobfuscator.config.ObfuscationConfig;
import com.fabricobfuscator.crypto.CryptoUtils;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.tree.*;

import java.security.SecureRandom;
import java.util.*;

/**
 * Продвинутый обфускатор строк с ватермарками Fix85
 * Шифрует все строковые литералы с динамической расшифровкой
 */
public class StringObfuscator {
    private final ObfuscationConfig config;
    private final SecureRandom random;
    private final Map<String, String> encryptedStrings = new HashMap<>();
    private final byte[] encryptionKey;
    
    // Ватермарка Fix85 встроенная в ключ шифрования
    private static final String WATERMARK_KEY = "Fix85_StringObfuscator_2024";
    
    public StringObfuscator(ObfuscationConfig config) {
        this.config = config;
        this.random = new SecureRandom();
        this.encryptionKey = generateEncryptionKey();
    }
    
    /**
     * Обфусцирует строки в классе
     */
    public void obfuscateStrings(ClassNode classNode) {
        // Добавляем декриптор в класс
        addStringDecryptor(classNode);
        
        // Обфусцируем строки во всех методах
        for (MethodNode method : classNode.methods) {
            obfuscateMethodStrings(method);
        }
        
        // Добавляем ватермарку Fix85 в зашифрованные строки
        addWatermarkStrings(classNode);
    }
    
    /**
     * Обфусцирует строки в методе
     */
    private void obfuscateMethodStrings(MethodNode method) {
        for (AbstractInsnNode insn : method.instructions.toArray()) {
            if (insn instanceof LdcInsnNode) {
                LdcInsnNode ldcInsn = (LdcInsnNode) insn;
                
                if (ldcInsn.cst instanceof String) {
                    String originalString = (String) ldcInsn.cst;
                    
                    // Пропускаем системные строки
                    if (shouldSkipString(originalString)) {
                        continue;
                    }
                    
                    // Шифруем строку
                    String encryptedString = encryptString(originalString);
                    
                    // Заменяем LDC инструкцию на вызов декриптора
                    InsnList replacement = createDecryptorCall(encryptedString);
                    method.instructions.insertBefore(insn, replacement);
                    method.instructions.remove(insn);
                }
            }
        }
    }
    
    /**
     * Создает вызов декриптора строк
     */
    private InsnList createDecryptorCall(String encryptedString) {
        InsnList instructions = new InsnList();
        
        // Загружаем зашифрованную строку
        instructions.add(new LdcInsnNode(encryptedString));
        
        // Вызываем декриптор
        instructions.add(new MethodInsnNode(
            Opcodes.INVOKESTATIC,
            "com/fabricobfuscator/runtime/StringDecryptor",
            "decrypt",
            "(Ljava/lang/String;)Ljava/lang/String;",
            false
        ));
        
        return instructions;
    }
    
    /**
     * Добавляет декриптор строк в класс
     */
    private void addStringDecryptor(ClassNode classNode) {
        // Создаем статическое поле с ключом шифрования
        FieldNode keyField = new FieldNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC | Opcodes.ACC_FINAL,
            "ᅟᅠᆞᆟ", // Скрытое имя с корейскими символами
            "[B",
            null,
            null
        );
        classNode.fields.add(keyField);
        
        // Создаем статический инициализатор для ключа
        MethodNode clinit = getOrCreateClinit(classNode);
        InsnList keyInit = new InsnList();
        
        // Инициализируем ключ с ватермаркой Fix85
        keyInit.add(new LdcInsnNode(CryptoUtils.encodeBase64(encryptionKey)));
        keyInit.add(new MethodInsnNode(
            Opcodes.INVOKESTATIC,
            "com/fabricobfuscator/crypto/CryptoUtils",
            "decodeBase64",
            "(Ljava/lang/String;)B",
            false
        ));
        keyInit.add(new FieldInsnNode(
            Opcodes.PUTSTATIC,
            classNode.name,
            "ᅟᅠᆞᆟ",
            "[B"
        ));
        
        clinit.instructions.insertBefore(
            clinit.instructions.getLast(),
            keyInit
        );
        
        // Создаем метод декриптора
        MethodNode decryptor = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            "ᅟᅠᆞᆟᄀ", // Скрытое имя
            "(Ljava/lang/String;)Ljava/lang/String;",
            null,
            null
        );
        
        decryptor.visitCode();
        
        // Загружаем зашифрованную строку
        decryptor.visitVarInsn(Opcodes.ALOAD, 0);
        
        // Декодируем из Base64
        decryptor.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            "com/fabricobfuscator/crypto/CryptoUtils",
            "decodeBase64",
            "(Ljava/lang/String;)[B",
            false
        );
        
        // Загружаем ключ
        decryptor.visitFieldInsn(
            Opcodes.GETSTATIC,
            classNode.name,
            "ᅟᅠᆞᆟ",
            "[B"
        );
        
        // Расшифровываем
        decryptor.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            "com/fabricobfuscator/crypto/CryptoUtils",
            "xorEncrypt",
            "([B[B)[B",
            false
        );
        
        // Преобразуем в строку
        decryptor.visitTypeInsn(Opcodes.NEW, "java/lang/String");
        decryptor.visitInsn(Opcodes.DUP_X1);
        decryptor.visitInsn(Opcodes.SWAP);
        decryptor.visitMethodInsn(
            Opcodes.INVOKESPECIAL,
            "java/lang/String",
            "<init>",
            "([B)V",
            false
        );
        
        decryptor.visitInsn(Opcodes.ARETURN);
        decryptor.visitMaxs(3, 1);
        decryptor.visitEnd();
        
        classNode.methods.add(decryptor);
    }
    
    /**
     * Добавляет ватермарки Fix85 в зашифрованные строки
     */
    private void addWatermarkStrings(ClassNode classNode) {
        // Добавляем скрытые строки с ватермарками
        String[] watermarkStrings = {
            "Obfuscated by Fix85",
            "Fix85 Advanced Obfuscator v1.0",
            "Protected by Fix85 Technology",
            "© 2024 Fix85 - All Rights Reserved"
        };
        
        for (String watermark : watermarkStrings) {
            String encrypted = encryptString(watermark);
            
            // Создаем скрытое поле с ватермаркой
            FieldNode watermarkField = new FieldNode(
                Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC | Opcodes.ACC_FINAL,
                generateHiddenFieldName(),
                "Ljava/lang/String;",
                null,
                encrypted
            );
            
            classNode.fields.add(watermarkField);
        }
    }
    
    /**
     * Шифрует строку
     */
    private String encryptString(String original) {
        if (encryptedStrings.containsKey(original)) {
            return encryptedStrings.get(original);
        }
        
        byte[] encrypted;
        
        switch (config.getStringObfuscationLevel()) {
            case 1:
                encrypted = simpleXorEncrypt(original.getBytes());
                break;
            case 2:
                encrypted = advancedXorEncrypt(original.getBytes());
                break;
            case 3:
                encrypted = multiLayerEncrypt(original.getBytes());
                break;
            default:
                encrypted = maximumSecurityEncrypt(original.getBytes());
                break;
        }
        
        String result = CryptoUtils.encodeBase64(encrypted);
        encryptedStrings.put(original, result);
        return result;
    }
    
    /**
     * Простое XOR шифрование
     */
    private byte[] simpleXorEncrypt(byte[] data) {
        return CryptoUtils.xorEncrypt(data, encryptionKey);
    }
    
    /**
     * Продвинутое XOR шифрование с ватермаркой
     */
    private byte[] advancedXorEncrypt(byte[] data) {
        // Добавляем ватермарку Fix85 в процесс шифрования
        byte[] watermarkKey = (WATERMARK_KEY + System.nanoTime()).getBytes();
        byte[] firstPass = CryptoUtils.xorEncrypt(data, encryptionKey);
        return CryptoUtils.xorEncrypt(firstPass, watermarkKey);
    }
    
    /**
     * Многослойное шифрование
     */
    private byte[] multiLayerEncrypt(byte[] data) {
        byte[] result = data;
        
        // Слой 1: XOR с основным ключом
        result = CryptoUtils.xorEncrypt(result, encryptionKey);
        
        // Слой 2: XOR с ватермаркой Fix85
        byte[] watermarkKey = WATERMARK_KEY.getBytes();
        result = CryptoUtils.xorEncrypt(result, watermarkKey);
        
        // Слой 3: XOR со случайным ключом
        byte[] randomKey = CryptoUtils.generateRandomKey(16);
        result = CryptoUtils.xorEncrypt(result, randomKey);
        
        // Добавляем случайный ключ в начало
        byte[] finalResult = new byte[randomKey.length + result.length];
        System.arraycopy(randomKey, 0, finalResult, 0, randomKey.length);
        System.arraycopy(result, 0, finalResult, randomKey.length, result.length);
        
        return finalResult;
    }
    
    /**
     * Максимальное шифрование с AES
     */
    private byte[] maximumSecurityEncrypt(byte[] data) {
        try {
            // Используем AES шифрование с ватермаркой Fix85
            byte[] aesKey = CryptoUtils.sha256((WATERMARK_KEY + "Fix85").getBytes());
            return CryptoUtils.encrypt(data, aesKey);
        } catch (Exception e) {
            // Fallback к многослойному шифрованию
            return multiLayerEncrypt(data);
        }
    }
    
    /**
     * Генерирует ключ шифрования с ватермаркой Fix85
     */
    private byte[] generateEncryptionKey() {
        String keySource = WATERMARK_KEY + System.currentTimeMillis() + random.nextLong();
        return CryptoUtils.sha256(keySource.getBytes());
    }
    
    /**
     * Проверяет, нужно ли пропустить строку
     */
    private boolean shouldSkipString(String str) {
        return str.length() < 2 ||
               str.startsWith("java.") ||
               str.startsWith("javax.") ||
               str.startsWith("net.minecraft.") ||
               str.startsWith("net.fabricmc.") ||
               str.matches("^[a-zA-Z_$][a-zA-Z0-9_$]*$"); // Простые идентификаторы
    }
    
    /**
     * Получает или создает статический инициализатор
     */
    private MethodNode getOrCreateClinit(ClassNode classNode) {
        for (MethodNode method : classNode.methods) {
            if (method.name.equals("<clinit>")) {
                return method;
            }
        }
        
        MethodNode clinit = new MethodNode(
            Opcodes.ACC_STATIC,
            "<clinit>",
            "()V",
            null,
            null
        );
        
        clinit.visitCode();
        clinit.visitInsn(Opcodes.RETURN);
        clinit.visitMaxs(0, 0);
        clinit.visitEnd();
        
        classNode.methods.add(clinit);
        return clinit;
    }
    
    /**
     * Генерирует скрытое имя поля
     */
    private String generateHiddenFieldName() {
        // Используем невидимые Unicode символы
        StringBuilder sb = new StringBuilder();
        String invisibleChars = "\u200B\u200C\u200D\u2060\uFEFF";
        
        for (int i = 0; i < 8; i++) {
            sb.append(invisibleChars.charAt(random.nextInt(invisibleChars.length())));
        }
        
        return sb.toString();
    }
    
    public Map<String, String> getEncryptedStrings() {
        return new HashMap<>(encryptedStrings);
    }
}
