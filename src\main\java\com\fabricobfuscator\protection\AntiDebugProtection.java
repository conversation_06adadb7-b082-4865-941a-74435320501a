package com.fabricobfuscator.protection;

import org.objectweb.asm.Label;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.tree.*;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;

/**
 * Система защиты от отладки и анализа Fix85
 * Встраивает множественные проверки для обнаружения попыток анализа
 */
public class AntiDebugProtection {
    private static final SecureRandom random = new SecureRandom();
    private static final String WATERMARK_AUTHOR = "Fix85";
    
    /**
     * Встраивает антиотладочную защиту в класс
     */
    public void addAntiDebugProtection(ClassNode classNode) {
        // 1. Добавляем проверки отладчика
        addDebuggerDetection(classNode);
        
        // 2. Добавляем проверки времени выполнения
        addTimingChecks(classNode);
        
        // 3. Добавляем проверки среды выполнения
        addEnvironmentChecks(classNode);
        
        // 4. Добавляем проверки целостности
        addIntegrityChecks(classNode);
        
        // 5. Встраиваем ложные проверки
        addDecoyChecks(classNode);
    }
    
    /**
     * Добавляет обнаружение отладчика
     */
    private void addDebuggerDetection(ClassNode classNode) {
        MethodNode detectionMethod = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedName() + "\u200BFix85\u200C", // Скрытая ватермарка
            "()Z",
            null,
            null
        );
        
        detectionMethod.visitCode();
        
        // Проверка 1: ManagementFactory для обнаружения отладчика
        detectionMethod.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            "java/lang/management/ManagementFactory",
            "getRuntimeMXBean",
            "()Ljava/lang/management/RuntimeMXBean;",
            false
        );
        detectionMethod.visitMethodInsn(
            Opcodes.INVOKEINTERFACE,
            "java/lang/management/RuntimeMXBean",
            "getInputArguments",
            "()Ljava/util/List;",
            true
        );
        detectionMethod.visitMethodInsn(
            Opcodes.INVOKEINTERFACE,
            "java/util/List",
            "toString",
            "()Ljava/lang/String;",
            true
        );
        
        // Проверяем наличие отладочных флагов
        detectionMethod.visitLdcInsn("jdwp");
        detectionMethod.visitMethodInsn(
            Opcodes.INVOKEVIRTUAL,
            "java/lang/String",
            "contains",
            "(Ljava/lang/CharSequence;)Z",
            false
        );
        
        Label notDebugging = new Label();
        detectionMethod.visitJumpInsn(Opcodes.IFEQ, notDebugging);

        // Если отладчик обнаружен - вызываем защитную реакцию
        addProtectionResponse(detectionMethod);

        detectionMethod.visitLabel(notDebugging);
        detectionMethod.visitInsn(Opcodes.ICONST_0);
        detectionMethod.visitInsn(Opcodes.IRETURN);
        
        detectionMethod.visitMaxs(2, 0);
        detectionMethod.visitEnd();
        
        classNode.methods.add(detectionMethod);
        
        // Встраиваем вызовы проверки в существующие методы
        embedDetectionCalls(classNode, detectionMethod.name);
    }
    
    /**
     * Добавляет проверки времени выполнения
     */
    private void addTimingChecks(ClassNode classNode) {
        MethodNode timingMethod = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedName(),
            "()V",
            null,
            null
        );
        
        timingMethod.visitCode();
        
        // Засекаем время начала
        timingMethod.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            "java/lang/System",
            "nanoTime",
            "()J",
            false
        );
        timingMethod.visitVarInsn(Opcodes.LSTORE, 0);
        
        // Выполняем простую операцию
        timingMethod.visitInsn(Opcodes.ICONST_1);
        timingMethod.visitInsn(Opcodes.ICONST_1);
        timingMethod.visitInsn(Opcodes.IADD);
        timingMethod.visitInsn(Opcodes.POP);
        
        // Засекаем время окончания
        timingMethod.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            "java/lang/System",
            "nanoTime",
            "()J",
            false
        );
        timingMethod.visitVarInsn(Opcodes.LLOAD, 0);
        timingMethod.visitInsn(Opcodes.LSUB);
        
        // Если время выполнения слишком большое (отладчик замедляет)
        timingMethod.visitLdcInsn(1000000L); // 1ms в наносекундах
        timingMethod.visitInsn(Opcodes.LCMP);
        
        Label normalTiming = new Label();
        timingMethod.visitJumpInsn(Opcodes.IFLE, normalTiming);

        // Защитная реакция
        addProtectionResponse(timingMethod);

        timingMethod.visitLabel(normalTiming);
        timingMethod.visitInsn(Opcodes.RETURN);
        
        timingMethod.visitMaxs(4, 2);
        timingMethod.visitEnd();
        
        classNode.methods.add(timingMethod);
    }
    
    /**
     * Добавляет проверки среды выполнения
     */
    private void addEnvironmentChecks(ClassNode classNode) {
        MethodNode envMethod = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedName(),
            "()V",
            null,
            null
        );
        
        envMethod.visitCode();
        
        // Проверяем системные свойства
        String[] suspiciousProperties = {
            "java.vm.name",
            "java.class.path",
            "user.dir"
        };
        
        for (String property : suspiciousProperties) {
            envMethod.visitLdcInsn(property);
            envMethod.visitMethodInsn(
                Opcodes.INVOKESTATIC,
                "java/lang/System",
                "getProperty",
                "(Ljava/lang/String;)Ljava/lang/String;",
                false
            );
            
            // Проверяем на наличие подозрительных строк
            String[] suspiciousStrings = {"debug", "jdb", "idea", "eclipse"};
            
            for (String suspicious : suspiciousStrings) {
                envMethod.visitInsn(Opcodes.DUP);
                envMethod.visitLdcInsn(suspicious);
                envMethod.visitMethodInsn(
                    Opcodes.INVOKEVIRTUAL,
                    "java/lang/String",
                    "toLowerCase",
                    "()Ljava/lang/String;",
                    false
                );
                envMethod.visitMethodInsn(
                    Opcodes.INVOKEVIRTUAL,
                    "java/lang/String",
                    "contains",
                    "(Ljava/lang/CharSequence;)Z",
                    false
                );
                
                Label safe = new Label();
                envMethod.visitJumpInsn(Opcodes.IFEQ, safe);

                // Защитная реакция
                addProtectionResponse(envMethod);

                envMethod.visitLabel(safe);
            }
            
            envMethod.visitInsn(Opcodes.POP); // Убираем значение свойства со стека
        }
        
        envMethod.visitInsn(Opcodes.RETURN);
        envMethod.visitMaxs(3, 0);
        envMethod.visitEnd();
        
        classNode.methods.add(envMethod);
    }
    
    /**
     * Добавляет проверки целостности с ватермаркой Fix85
     */
    private void addIntegrityChecks(ClassNode classNode) {
        MethodNode integrityMethod = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedName() + "Fix85",
            "()V",
            null,
            null
        );
        
        integrityMethod.visitCode();
        
        // Вычисляем хеш класса
        integrityMethod.visitLdcInsn(classNode.name);
        integrityMethod.visitMethodInsn(
            Opcodes.INVOKEVIRTUAL,
            "java/lang/String",
            "hashCode",
            "()I",
            false
        );
        
        // Сравниваем с ожидаемым значением (включает ватермарку Fix85)
        int expectedHash = (classNode.name + WATERMARK_AUTHOR).hashCode();
        integrityMethod.visitLdcInsn(expectedHash);
        
        Label integrityOk = new Label();
        integrityMethod.visitJumpInsn(Opcodes.IF_ICMPEQ, integrityOk);

        // Если целостность нарушена
        addProtectionResponse(integrityMethod);

        integrityMethod.visitLabel(integrityOk);
        integrityMethod.visitInsn(Opcodes.RETURN);
        
        integrityMethod.visitMaxs(2, 0);
        integrityMethod.visitEnd();
        
        classNode.methods.add(integrityMethod);
    }
    
    /**
     * Добавляет ложные проверки для запутывания
     */
    private void addDecoyChecks(ClassNode classNode) {
        // Создаем несколько ложных методов проверки
        for (int i = 0; i < 3; i++) {
            MethodNode decoyMethod = new MethodNode(
                Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
                generateObfuscatedName(),
                "()Z",
                null,
                null
            );
            
            decoyMethod.visitCode();
            
            // Ложные проверки, которые всегда возвращают true
            decoyMethod.visitMethodInsn(
                Opcodes.INVOKESTATIC,
                "java/lang/System",
                "currentTimeMillis",
                "()J",
                false
            );
            decoyMethod.visitLdcInsn(0L);
            decoyMethod.visitInsn(Opcodes.LCMP);
            decoyMethod.visitJumpInsn(Opcodes.IFLE, new Label());
            
            decoyMethod.visitInsn(Opcodes.ICONST_1);
            decoyMethod.visitInsn(Opcodes.IRETURN);
            
            decoyMethod.visitMaxs(4, 0);
            decoyMethod.visitEnd();
            
            classNode.methods.add(decoyMethod);
        }
    }
    
    /**
     * Добавляет защитную реакцию при обнаружении отладки
     */
    private void addProtectionResponse(MethodNode method) {
        // Вариант 1: Тихий выход
        if (random.nextBoolean()) {
            method.visitMethodInsn(
                Opcodes.INVOKESTATIC,
                "java/lang/System",
                "exit",
                "(I)V",
                false
            );
        } else {
            // Вариант 2: Исключение с ватермаркой Fix85
            method.visitTypeInsn(Opcodes.NEW, "java/lang/RuntimeException");
            method.visitInsn(Opcodes.DUP);
            method.visitLdcInsn("Protected by " + WATERMARK_AUTHOR);
            method.visitMethodInsn(
                Opcodes.INVOKESPECIAL,
                "java/lang/RuntimeException",
                "<init>",
                "(Ljava/lang/String;)V",
                false
            );
            method.visitInsn(Opcodes.ATHROW);
        }
    }
    
    /**
     * Встраивает вызовы проверок в существующие методы
     */
    private void embedDetectionCalls(ClassNode classNode, String detectionMethodName) {
        for (MethodNode method : classNode.methods) {
            if (method.name.equals("<init>") || method.name.equals("<clinit>") || 
                method.name.equals(detectionMethodName)) {
                continue;
            }
            
            // Добавляем вызов проверки в начало метода
            InsnList detection = new InsnList();
            detection.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                classNode.name,
                detectionMethodName,
                "()Z",
                false
            ));
            detection.add(new InsnNode(Opcodes.POP)); // Убираем результат
            
            if (method.instructions.size() > 0) {
                method.instructions.insertBefore(method.instructions.getFirst(), detection);
            }
        }
    }
    
    /**
     * Генерирует обфусцированное имя метода
     */
    private String generateObfuscatedName() {
        StringBuilder sb = new StringBuilder();
        
        // Используем невидимые Unicode символы
        String invisibleChars = "\u200B\u200C\u200D\u2060\uFEFF";
        
        for (int i = 0; i < 8 + random.nextInt(8); i++) {
            if (random.nextBoolean()) {
                sb.append((char) ('a' + random.nextInt(26)));
            } else {
                sb.append(invisibleChars.charAt(random.nextInt(invisibleChars.length())));
            }
        }
        
        return sb.toString();
    }
}
