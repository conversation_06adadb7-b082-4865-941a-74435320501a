package com.fabricobfuscator.protection;

import com.fabricobfuscator.crypto.CryptoUtils;
import org.objectweb.asm.ClassWriter;
import org.objectweb.asm.MethodVisitor;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.FieldNode;
import org.objectweb.asm.tree.MethodNode;

import java.security.SecureRandom;
import java.util.*;

/**
 * Многоуровневая система ватермарок Fix85
 * Встраивает скрытые метки авторства в обфусцированный код
 */
public class WatermarkSystem {
    private static final String AUTHOR = "Fix85";
    private static final String WATERMARK_SIGNATURE = "F1x85_0bfu5c4t0r_2024";
    private static final SecureRandom random = new SecureRandom();
    
    // Скрытые ватермарки в Unicode
    private static final String[] HIDDEN_WATERMARKS = {
        "\u200BFix85\u200C", // Невидимые символы
        "\u2060F\u200Di\u200Bx\u200C8\u20605\u200D", // Разделенная ватермарка
        "Ḟḯẋ⁸⁵", // Диакритические знаки
        "𝐅𝐢𝐱𝟖𝟓", // Математические символы
        "ꟻⅈẋ𝟖𝟓" // Альтернативные символы
    };
    
    /**
     * Встраивает ватермарки в класс
     */
    public void embedWatermarks(ClassNode classNode) {
        // 1. Скрытое поле с ватермаркой
        embedHiddenField(classNode);
        
        // 2. Ватермарка в именах методов
        embedMethodNameWatermarks(classNode);
        
        // 3. Ватермарка в константах
        embedConstantWatermarks(classNode);
        
        // 4. Ватермарка в байт-коде
        embedBytecodeWatermarks(classNode);
        
        // 5. Временная ватермарка
        embedTimestampWatermark(classNode);
    }
    
    /**
     * Встраивает скрытое поле с ватермаркой
     */
    private void embedHiddenField(ClassNode classNode) {
        String hiddenWatermark = HIDDEN_WATERMARKS[random.nextInt(HIDDEN_WATERMARKS.length)];
        String fieldName = generateObfuscatedFieldName() + hiddenWatermark;
        
        // Создаем скрытое статическое поле
        FieldNode watermarkField = new FieldNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC | Opcodes.ACC_FINAL,
            fieldName,
            "Ljava/lang/String;",
            null,
            encryptWatermark(WATERMARK_SIGNATURE)
        );
        
        classNode.fields.add(watermarkField);
    }
    
    /**
     * Встраивает ватермарки в имена методов
     */
    private void embedMethodNameWatermarks(ClassNode classNode) {
        // Добавляем фиктивные методы с ватермарками в именах
        for (int i = 0; i < 3; i++) {
            String watermark = HIDDEN_WATERMARKS[i % HIDDEN_WATERMARKS.length];
            String methodName = generateObfuscatedMethodName() + watermark;
            
            MethodNode watermarkMethod = new MethodNode(
                Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
                methodName,
                "()V",
                null,
                null
            );
            
            // Добавляем пустое тело метода
            watermarkMethod.visitCode();
            watermarkMethod.visitInsn(Opcodes.RETURN);
            watermarkMethod.visitMaxs(0, 0);
            watermarkMethod.visitEnd();
            
            classNode.methods.add(watermarkMethod);
        }
    }
    
    /**
     * Встраивает ватермарки в константы
     */
    private void embedConstantWatermarks(ClassNode classNode) {
        // Создаем метод с зашифрованными константами-ватермарками
        MethodNode watermarkMethod = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedMethodName(),
            "()Ljava/lang/String;",
            null,
            null
        );
        
        watermarkMethod.visitCode();
        
        // Встраиваем зашифрованную ватермарку как константу
        String encryptedWatermark = encryptWatermark(AUTHOR + "_" + System.currentTimeMillis());
        watermarkMethod.visitLdcInsn(encryptedWatermark);
        
        // Добавляем проверку целостности
        watermarkMethod.visitLdcInsn(calculateChecksum(encryptedWatermark));
        watermarkMethod.visitMethodInsn(Opcodes.INVOKESTATIC, 
            classNode.name, 
            "validateWatermark", 
            "(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;", 
            false);
        
        watermarkMethod.visitInsn(Opcodes.ARETURN);
        watermarkMethod.visitMaxs(2, 0);
        watermarkMethod.visitEnd();
        
        classNode.methods.add(watermarkMethod);
        
        // Добавляем метод валидации
        addWatermarkValidator(classNode);
    }
    
    /**
     * Встраивает ватермарки в байт-код
     */
    private void embedBytecodeWatermarks(ClassNode classNode) {
        // Находим существующие методы и встраиваем ватермарки
        for (MethodNode method : classNode.methods) {
            if (method.name.equals("<init>") || method.name.equals("<clinit>")) {
                continue;
            }
            
            // Встраиваем скрытые инструкции-ватермарки
            embedHiddenInstructions(method);
        }
    }
    
    /**
     * Встраивает скрытые инструкции в метод
     */
    private void embedHiddenInstructions(MethodNode method) {
        // Добавляем в начало метода скрытую проверку ватермарки
        method.visitCode();
        
        // Загружаем зашифрованную ватермарку
        String hiddenWatermark = encryptWatermark("Fix85_" + method.name);
        method.visitLdcInsn(hiddenWatermark);
        
        // Вызываем фиктивную проверку (которая ничего не делает)
        method.visitMethodInsn(Opcodes.INVOKESTATIC, 
            "java/lang/String", 
            "valueOf", 
            "(Ljava/lang/Object;)Ljava/lang/String;", 
            false);
        method.visitInsn(Opcodes.POP); // Убираем результат со стека
    }
    
    /**
     * Встраивает временную ватермарку
     */
    private void embedTimestampWatermark(ClassNode classNode) {
        long timestamp = System.currentTimeMillis();
        String timestampWatermark = AUTHOR + "_" + Long.toHexString(timestamp);
        
        // Создаем скрытое поле с временной меткой
        FieldNode timestampField = new FieldNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC | Opcodes.ACC_FINAL,
            generateObfuscatedFieldName() + HIDDEN_WATERMARKS[0],
            "J",
            null,
            timestamp
        );
        
        classNode.fields.add(timestampField);
        
        // Добавляем метод для проверки временной метки
        addTimestampValidator(classNode, timestamp);
    }
    
    /**
     * Добавляет валидатор ватермарки
     */
    private void addWatermarkValidator(ClassNode classNode) {
        MethodNode validator = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            "validateWatermark",
            "(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;",
            null,
            null
        );
        
        validator.visitCode();
        
        // Простая проверка контрольной суммы
        validator.visitVarInsn(Opcodes.ALOAD, 0); // watermark
        validator.visitVarInsn(Opcodes.ALOAD, 1); // checksum
        
        // Вычисляем контрольную сумму и сравниваем
        validator.visitVarInsn(Opcodes.ALOAD, 0);
        validator.visitMethodInsn(Opcodes.INVOKEVIRTUAL, 
            "java/lang/String", 
            "hashCode", 
            "()I", 
            false);
        validator.visitMethodInsn(Opcodes.INVOKESTATIC, 
            "java/lang/String", 
            "valueOf", 
            "(I)Ljava/lang/String;", 
            false);
        
        validator.visitVarInsn(Opcodes.ALOAD, 1);
        validator.visitMethodInsn(Opcodes.INVOKEVIRTUAL, 
            "java/lang/String", 
            "equals", 
            "(Ljava/lang/Object;)Z", 
            false);
        
        // Если проверка не прошла, возвращаем пустую строку
        validator.visitVarInsn(Opcodes.ALOAD, 0);
        validator.visitInsn(Opcodes.ARETURN);
        
        validator.visitMaxs(3, 2);
        validator.visitEnd();
        
        classNode.methods.add(validator);
    }
    
    /**
     * Добавляет валидатор временной метки
     */
    private void addTimestampValidator(ClassNode classNode, long originalTimestamp) {
        MethodNode validator = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedMethodName(),
            "()Z",
            null,
            null
        );
        
        validator.visitCode();
        
        // Проверяем, что временная метка соответствует ожидаемой
        validator.visitLdcInsn(originalTimestamp);
        validator.visitMethodInsn(Opcodes.INVOKESTATIC, 
            "java/lang/System", 
            "currentTimeMillis", 
            "()J", 
            false);
        validator.visitInsn(Opcodes.LCMP);
        validator.visitInsn(Opcodes.ICONST_1);
        validator.visitInsn(Opcodes.IRETURN);
        
        validator.visitMaxs(4, 0);
        validator.visitEnd();
        
        classNode.methods.add(validator);
    }
    
    /**
     * Шифрует ватермарку
     */
    private String encryptWatermark(String watermark) {
        try {
            byte[] encrypted = CryptoUtils.encrypt(watermark.getBytes(), AUTHOR.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            // Fallback: простое XOR шифрование
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < watermark.length(); i++) {
                result.append((char) (watermark.charAt(i) ^ AUTHOR.charAt(i % AUTHOR.length())));
            }
            return Base64.getEncoder().encodeToString(result.toString().getBytes());
        }
    }
    
    /**
     * Вычисляет контрольную сумму
     */
    private String calculateChecksum(String data) {
        return String.valueOf(data.hashCode() ^ AUTHOR.hashCode());
    }
    
    /**
     * Генерирует обфусцированное имя поля
     */
    private String generateObfuscatedFieldName() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            sb.append((char) ('a' + random.nextInt(26)));
        }
        return sb.toString();
    }
    
    /**
     * Генерирует обфусцированное имя метода
     */
    private String generateObfuscatedMethodName() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            if (random.nextBoolean()) {
                sb.append((char) ('a' + random.nextInt(26)));
            } else {
                sb.append((char) ('A' + random.nextInt(26)));
            }
        }
        return sb.toString();
    }
    
    /**
     * Проверяет наличие ватермарки в классе
     */
    public boolean hasWatermark(ClassNode classNode) {
        // Проверяем наличие скрытых полей с ватермарками
        for (FieldNode field : classNode.fields) {
            for (String watermark : HIDDEN_WATERMARKS) {
                if (field.name.contains(watermark)) {
                    return true;
                }
            }
        }
        
        // Проверяем методы
        for (MethodNode method : classNode.methods) {
            for (String watermark : HIDDEN_WATERMARKS) {
                if (method.name.contains(watermark)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Извлекает информацию о ватермарке
     */
    public String extractWatermarkInfo(ClassNode classNode) {
        StringBuilder info = new StringBuilder();
        info.append("Обфусцировано Fix85 Obfuscator\n");
        info.append("Автор: ").append(AUTHOR).append("\n");
        info.append("Подпись: ").append(WATERMARK_SIGNATURE).append("\n");
        
        // Ищем временную метку
        for (FieldNode field : classNode.fields) {
            if (field.value instanceof Long) {
                long timestamp = (Long) field.value;
                info.append("Время обфускации: ").append(new Date(timestamp)).append("\n");
                break;
            }
        }
        
        return info.toString();
    }
}
