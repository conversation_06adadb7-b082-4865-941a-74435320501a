package com.fabricobfuscator.protection;

import com.fabricobfuscator.crypto.CryptoUtils;
import org.objectweb.asm.Label;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.tree.*;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

/**
 * Система контрольных сумм и самопроверки Fix85
 * Встраивает проверки целостности кода во время выполнения
 */
public class ChecksumProtection {
    private static final SecureRandom random = new SecureRandom();
    private static final String WATERMARK_SIGNATURE = "Fix85_Checksum_Protection";
    
    private final Map<String, String> classChecksums = new HashMap<>();
    private final Map<String, String> methodChecksums = new HashMap<>();
    
    /**
     * Встраивает систему контрольных сумм в класс
     */
    public void addChecksumProtection(ClassNode classNode) {
        // 1. Вычисляем контрольные суммы
        calculateChecksums(classNode);
        
        // 2. Добавляем поля для хранения контрольных сумм
        addChecksumFields(classNode);
        
        // 3. Добавляем методы проверки
        addValidationMethods(classNode);
        
        // 4. Встраиваем проверки в методы
        embedValidationCalls(classNode);
        
        // 5. Добавляем самопроверку класса
        addSelfValidation(classNode);
    }
    
    /**
     * Вычисляет контрольные суммы для класса и методов
     */
    private void calculateChecksums(ClassNode classNode) {
        // Контрольная сумма класса (включает ватермарку Fix85)
        String classData = classNode.name + classNode.superName + WATERMARK_SIGNATURE;
        String classChecksum = calculateHash(classData);
        classChecksums.put(classNode.name, classChecksum);
        
        // Контрольные суммы методов
        for (MethodNode method : classNode.methods) {
            String methodData = method.name + method.desc + method.access + WATERMARK_SIGNATURE;
            String methodChecksum = calculateHash(methodData);
            methodChecksums.put(classNode.name + "." + method.name, methodChecksum);
        }
    }
    
    /**
     * Добавляет поля для хранения контрольных сумм
     */
    private void addChecksumFields(ClassNode classNode) {
        // Скрытое поле с контрольной суммой класса
        String classChecksumField = generateHiddenFieldName() + "\u200BFix85\u200C";
        FieldNode classField = new FieldNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC | Opcodes.ACC_FINAL,
            classChecksumField,
            "Ljava/lang/String;",
            null,
            encryptChecksum(classChecksums.get(classNode.name))
        );
        classNode.fields.add(classField);
        
        // Скрытое поле с контрольными суммами методов
        String methodChecksumField = generateHiddenFieldName() + "\u200DFix85\u200B";
        FieldNode methodField = new FieldNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC | Opcodes.ACC_FINAL,
            methodChecksumField,
            "Ljava/util/Map;",
            "Ljava/util/Map<Ljava/lang/String;Ljava/lang/String;>;",
            null
        );
        classNode.fields.add(methodField);
        
        // Инициализируем карту контрольных сумм в статическом блоке
        initializeChecksumMap(classNode, methodChecksumField);
    }
    
    /**
     * Инициализирует карту контрольных сумм методов
     */
    private void initializeChecksumMap(ClassNode classNode, String fieldName) {
        MethodNode clinit = getOrCreateClinit(classNode);
        
        InsnList initialization = new InsnList();
        
        // Создаем HashMap
        initialization.add(new TypeInsnNode(Opcodes.NEW, "java/util/HashMap"));
        initialization.add(new InsnNode(Opcodes.DUP));
        initialization.add(new MethodInsnNode(
            Opcodes.INVOKESPECIAL,
            "java/util/HashMap",
            "<init>",
            "()V",
            false
        ));
        
        // Добавляем контрольные суммы методов
        for (Map.Entry<String, String> entry : methodChecksums.entrySet()) {
            if (entry.getKey().startsWith(classNode.name + ".")) {
                String methodName = entry.getKey().substring(classNode.name.length() + 1);
                String checksum = entry.getValue();
                
                initialization.add(new InsnNode(Opcodes.DUP));
                initialization.add(new LdcInsnNode(methodName));
                initialization.add(new LdcInsnNode(encryptChecksum(checksum)));
                initialization.add(new MethodInsnNode(
                    Opcodes.INVOKEINTERFACE,
                    "java/util/Map",
                    "put",
                    "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;",
                    true
                ));
                initialization.add(new InsnNode(Opcodes.POP)); // Убираем возвращаемое значение
            }
        }
        
        // Сохраняем в поле
        initialization.add(new FieldInsnNode(
            Opcodes.PUTSTATIC,
            classNode.name,
            fieldName,
            "Ljava/util/Map;"
        ));
        
        // Вставляем перед return в clinit
        AbstractInsnNode returnInsn = clinit.instructions.getLast();
        clinit.instructions.insertBefore(returnInsn, initialization);
    }
    
    /**
     * Добавляет методы проверки контрольных сумм
     */
    private void addValidationMethods(ClassNode classNode) {
        // Метод проверки класса
        addClassValidationMethod(classNode);
        
        // Метод проверки методов
        addMethodValidationMethod(classNode);
        
        // Общий метод валидации
        addGeneralValidationMethod(classNode);
    }
    
    /**
     * Добавляет метод проверки контрольной суммы класса
     */
    private void addClassValidationMethod(ClassNode classNode) {
        MethodNode validator = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedMethodName() + "Fix85",
            "()Z",
            null,
            null
        );
        
        validator.visitCode();
        
        // Получаем сохраненную контрольную сумму
        String checksumField = findChecksumField(classNode, "\u200BFix85\u200C");
        validator.visitFieldInsn(
            Opcodes.GETSTATIC,
            classNode.name,
            checksumField,
            "Ljava/lang/String;"
        );
        
        // Расшифровываем
        validator.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            classNode.name,
            "decryptChecksum",
            "(Ljava/lang/String;)Ljava/lang/String;",
            false
        );
        
        // Вычисляем текущую контрольную сумму
        String currentData = classNode.name + classNode.superName + WATERMARK_SIGNATURE;
        validator.visitLdcInsn(calculateHash(currentData));
        
        // Сравниваем
        validator.visitMethodInsn(
            Opcodes.INVOKEVIRTUAL,
            "java/lang/String",
            "equals",
            "(Ljava/lang/Object;)Z",
            false
        );
        
        Label validLabel = new Label();
        validator.visitJumpInsn(Opcodes.IFNE, validLabel);

        // Если контрольная сумма не совпадает - защитная реакция
        addProtectionResponse(validator);

        validator.visitLabel(validLabel);
        validator.visitInsn(Opcodes.ICONST_1);
        validator.visitInsn(Opcodes.IRETURN);
        
        validator.visitMaxs(2, 0);
        validator.visitEnd();
        
        classNode.methods.add(validator);
        
        // Добавляем метод расшифровки контрольных сумм
        addDecryptionMethod(classNode);
    }
    
    /**
     * Добавляет метод проверки контрольных сумм методов
     */
    private void addMethodValidationMethod(ClassNode classNode) {
        MethodNode validator = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            generateObfuscatedMethodName(),
            "(Ljava/lang/String;)Z",
            null,
            null
        );
        
        validator.visitCode();
        
        // Получаем карту контрольных сумм
        String mapField = findChecksumField(classNode, "\u200DFix85\u200B");
        validator.visitFieldInsn(
            Opcodes.GETSTATIC,
            classNode.name,
            mapField,
            "Ljava/util/Map;"
        );
        
        // Получаем контрольную сумму для метода
        validator.visitVarInsn(Opcodes.ALOAD, 0); // имя метода
        validator.visitMethodInsn(
            Opcodes.INVOKEINTERFACE,
            "java/util/Map",
            "get",
            "(Ljava/lang/Object;)Ljava/lang/Object;",
            true
        );
        validator.visitTypeInsn(Opcodes.CHECKCAST, "java/lang/String");
        
        // Проверяем, что контрольная сумма существует
        validator.visitInsn(Opcodes.DUP);
        Label existsLabel = new Label();
        validator.visitJumpInsn(Opcodes.IFNONNULL, existsLabel);

        // Если контрольной суммы нет - возвращаем false
        validator.visitInsn(Opcodes.POP);
        validator.visitInsn(Opcodes.ICONST_0);
        validator.visitInsn(Opcodes.IRETURN);

        validator.visitLabel(existsLabel);
        
        // Расшифровываем и проверяем (упрощенная версия)
        validator.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            classNode.name,
            "decryptChecksum",
            "(Ljava/lang/String;)Ljava/lang/String;",
            false
        );
        
        // Для простоты всегда возвращаем true (в реальной реализации здесь была бы полная проверка)
        validator.visitInsn(Opcodes.POP);
        validator.visitInsn(Opcodes.ICONST_1);
        validator.visitInsn(Opcodes.IRETURN);
        
        validator.visitMaxs(3, 1);
        validator.visitEnd();
        
        classNode.methods.add(validator);
    }
    
    /**
     * Добавляет общий метод валидации
     */
    private void addGeneralValidationMethod(ClassNode classNode) {
        MethodNode validator = new MethodNode(
            Opcodes.ACC_PUBLIC | Opcodes.ACC_STATIC,
            generateObfuscatedMethodName() + "Validate",
            "()V",
            null,
            null
        );
        
        validator.visitCode();
        
        // Проверяем класс
        validator.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            classNode.name,
            findValidationMethod(classNode, "Fix85"),
            "()Z",
            false
        );
        
        Label classOkLabel = new Label();
        validator.visitJumpInsn(Opcodes.IFNE, classOkLabel);

        // Защитная реакция
        addProtectionResponse(validator);

        validator.visitLabel(classOkLabel);
        validator.visitInsn(Opcodes.RETURN);
        
        validator.visitMaxs(1, 0);
        validator.visitEnd();
        
        classNode.methods.add(validator);
    }
    
    /**
     * Добавляет метод расшифровки контрольных сумм
     */
    private void addDecryptionMethod(ClassNode classNode) {
        MethodNode decryptor = new MethodNode(
            Opcodes.ACC_PRIVATE | Opcodes.ACC_STATIC,
            "decryptChecksum",
            "(Ljava/lang/String;)Ljava/lang/String;",
            null,
            null
        );
        
        decryptor.visitCode();
        
        // Простая расшифровка (XOR с ключом Fix85)
        decryptor.visitVarInsn(Opcodes.ALOAD, 0);
        decryptor.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            "com/fabricobfuscator/crypto/CryptoUtils",
            "decodeBase64",
            "(Ljava/lang/String;)[B",
            false
        );
        
        decryptor.visitLdcInsn(WATERMARK_SIGNATURE);
        decryptor.visitMethodInsn(
            Opcodes.INVOKEVIRTUAL,
            "java/lang/String",
            "getBytes",
            "()[B",
            false
        );
        
        decryptor.visitMethodInsn(
            Opcodes.INVOKESTATIC,
            "com/fabricobfuscator/crypto/CryptoUtils",
            "xorEncrypt",
            "([B[B)[B",
            false
        );
        
        decryptor.visitTypeInsn(Opcodes.NEW, "java/lang/String");
        decryptor.visitInsn(Opcodes.DUP_X1);
        decryptor.visitInsn(Opcodes.SWAP);
        decryptor.visitMethodInsn(
            Opcodes.INVOKESPECIAL,
            "java/lang/String",
            "<init>",
            "([B)V",
            false
        );
        
        decryptor.visitInsn(Opcodes.ARETURN);
        decryptor.visitMaxs(3, 1);
        decryptor.visitEnd();
        
        classNode.methods.add(decryptor);
    }
    
    /**
     * Встраивает вызовы проверки в методы
     */
    private void embedValidationCalls(ClassNode classNode) {
        String validationMethod = findValidationMethod(classNode, "Validate");
        
        for (MethodNode method : classNode.methods) {
            if (method.name.equals("<clinit>") || 
                method.name.contains("Fix85") ||
                method.name.equals("decryptChecksum") ||
                method.name.equals(validationMethod)) {
                continue;
            }
            
            // Добавляем проверку в случайные места метода
            if (random.nextFloat() < 0.3f && method.instructions.size() > 5) {
                InsnList validation = new InsnList();
                validation.add(new MethodInsnNode(
                    Opcodes.INVOKESTATIC,
                    classNode.name,
                    validationMethod,
                    "()V",
                    false
                ));
                
                // Вставляем в случайное место
                int insertPos = random.nextInt(method.instructions.size() / 2);
                AbstractInsnNode insertPoint = method.instructions.get(insertPos);
                method.instructions.insertBefore(insertPoint, validation);
            }
        }
    }
    
    /**
     * Добавляет самопроверку класса
     */
    private void addSelfValidation(ClassNode classNode) {
        // Добавляем вызов проверки в статический инициализатор
        MethodNode clinit = getOrCreateClinit(classNode);
        
        InsnList selfCheck = new InsnList();
        String validationMethod = findValidationMethod(classNode, "Validate");
        
        selfCheck.add(new MethodInsnNode(
            Opcodes.INVOKESTATIC,
            classNode.name,
            validationMethod,
            "()V",
            false
        ));
        
        // Вставляем в конец clinit (перед return)
        AbstractInsnNode returnInsn = clinit.instructions.getLast();
        clinit.instructions.insertBefore(returnInsn, selfCheck);
    }
    
    // Вспомогательные методы
    
    private String calculateHash(String data) {
        byte[] hash = CryptoUtils.sha256(data.getBytes());
        return CryptoUtils.encodeBase64(hash);
    }
    
    private String encryptChecksum(String checksum) {
        byte[] encrypted = CryptoUtils.xorEncrypt(
            checksum.getBytes(), 
            WATERMARK_SIGNATURE.getBytes()
        );
        return CryptoUtils.encodeBase64(encrypted);
    }
    
    private String generateHiddenFieldName() {
        StringBuilder sb = new StringBuilder();
        String invisibleChars = "\u200B\u200C\u200D\u2060\uFEFF";
        
        for (int i = 0; i < 6; i++) {
            sb.append(invisibleChars.charAt(random.nextInt(invisibleChars.length())));
        }
        
        return sb.toString();
    }
    
    private String generateObfuscatedMethodName() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            sb.append((char) ('a' + random.nextInt(26)));
        }
        return sb.toString();
    }
    
    private String findChecksumField(ClassNode classNode, String marker) {
        for (FieldNode field : classNode.fields) {
            if (field.name.contains(marker)) {
                return field.name;
            }
        }
        return null;
    }
    
    private String findValidationMethod(ClassNode classNode, String marker) {
        for (MethodNode method : classNode.methods) {
            if (method.name.contains(marker)) {
                return method.name;
            }
        }
        return null;
    }
    
    private MethodNode getOrCreateClinit(ClassNode classNode) {
        for (MethodNode method : classNode.methods) {
            if (method.name.equals("<clinit>")) {
                return method;
            }
        }
        
        MethodNode clinit = new MethodNode(
            Opcodes.ACC_STATIC,
            "<clinit>",
            "()V",
            null,
            null
        );
        
        clinit.visitCode();
        clinit.visitInsn(Opcodes.RETURN);
        clinit.visitMaxs(0, 0);
        clinit.visitEnd();
        
        classNode.methods.add(clinit);
        return clinit;
    }
    
    private void addProtectionResponse(MethodNode method) {
        // Выбрасываем исключение с ватермаркой Fix85
        method.visitTypeInsn(Opcodes.NEW, "java/lang/SecurityException");
        method.visitInsn(Opcodes.DUP);
        method.visitLdcInsn("Integrity check failed - Protected by Fix85");
        method.visitMethodInsn(
            Opcodes.INVOKESPECIAL,
            "java/lang/SecurityException",
            "<init>",
            "(Ljava/lang/String;)V",
            false
        );
        method.visitInsn(Opcodes.ATHROW);
    }
}
