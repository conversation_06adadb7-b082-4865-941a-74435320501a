package com.fabricobfuscator;

import com.fabricobfuscator.config.ObfuscationConfig;
import com.fabricobfuscator.core.ObfuscationEngine;
import com.fabricobfuscator.utils.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Главный класс обфускатора для Fabric модов
 * Обеспечивает максимальную защиту от декомпиляции и анализа
 */
public class FabricObfuscator {
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(FabricObfuscator.class);
    
    public static void main(String[] args) {
        printBanner();
        
        if (args.length < 2) {
            printUsage();
            System.exit(1);
        }
        
        try {
            String inputPath = args[0];
            String outputPath = args[1];
            String configPath = args.length > 2 ? args[2] : "obfuscation.json";
            
            // Проверяем входной файл
            Path input = Paths.get(inputPath);
            if (!Files.exists(input)) {
                LOGGER.error("Входной файл не найден: {}", inputPath);
                System.exit(1);
            }
            
            // Загружаем конфигурацию
            ObfuscationConfig config = ObfuscationConfig.load(configPath);
            LOGGER.info("Конфигурация загружена: {}", configPath);
            
            // Создаем движок обфускации
            ObfuscationEngine engine = new ObfuscationEngine(config);
            
            // Запускаем обфускацию
            LOGGER.info("Начинаем обфускацию: {} -> {}", inputPath, outputPath);
            long startTime = System.currentTimeMillis();
            
            engine.obfuscate(input, Paths.get(outputPath));
            
            long endTime = System.currentTimeMillis();
            LOGGER.info("Обфускация завершена за {} мс", endTime - startTime);
            
        } catch (Exception e) {
            LOGGER.error("Ошибка при обфускации", e);
            System.exit(1);
        }
    }
    
    private static void printBanner() {
        System.out.println("╔══════════════════════════════════════════════════════════════╗");
        System.out.println("║                    FABRIC OBFUSCATOR v1.0                   ║");
        System.out.println("║              Максимальная защита Fabric модов                ║");
        System.out.println("║                                                              ║");
        System.out.println("║  • Многоуровневая обфускация имен                           ║");
        System.out.println("║  • Шифрование строковых литералов                           ║");
        System.out.println("║  • Защита от отладки и анализа                              ║");
        System.out.println("║  • Контрольные суммы и самопроверка                         ║");
        System.out.println("║  • Усложнение потока управления                             ║");
        System.out.println("╚══════════════════════════════════════════════════════════════╝");
        System.out.println();
    }
    
    private static void printUsage() {
        System.out.println("Использование:");
        System.out.println("  java -jar fabric-obfuscator.jar <input.jar> <output.jar> [config.json]");
        System.out.println();
        System.out.println("Параметры:");
        System.out.println("  input.jar   - Входной JAR файл мода");
        System.out.println("  output.jar  - Выходной обфусцированный JAR файл");
        System.out.println("  config.json - Файл конфигурации (опционально)");
        System.out.println();
        System.out.println("Примеры:");
        System.out.println("  java -jar fabric-obfuscator.jar mymod.jar mymod-obfuscated.jar");
        System.out.println("  java -jar fabric-obfuscator.jar mymod.jar mymod-obfuscated.jar custom-config.json");
    }
}
