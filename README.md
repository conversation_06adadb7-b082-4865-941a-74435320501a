# Fix85 Fabric Obfuscator

**Профессиональный обфускатор для Fabric модов с максимальной защитой**

Создан **Fix85** - продвинутая система обфускации с множественными уровнями защиты, которая делает декомпиляцию и анализ кода практически невозможными.

## 🔥 Особенности

### 🛡️ Многоуровневая защита
- **5 уровней обфускации имен** - от простых до экстремально сложных Unicode символов
- **4 уровня шифрования строк** - от XOR до AES с многослойным шифрованием
- **Встроенные ватермарки Fix85** - скрытые метки авторства в коде
- **Антиотладочная защита** - блокирует попытки анализа
- **Контрольные суммы** - проверка целостности во время выполнения

### 🎯 Специализация для Fabric
- Автоматическое исключение Minecraft и Fabric API классов
- Сохранение совместимости с Mixin системой
- Поддержка всех версий Fabric

### 🔐 Ватермарки Fix85
- Скрытые метки в именах классов и методов
- Зашифрованные константы с подписью автора
- Временные метки обфускации
- Невидимые Unicode символы
- Проверка целостности ватермарок

## 🚀 Быстрый старт

### Сборка
```bash
./gradlew build
```

### Использование
```bash
java -jar fabric-obfuscator.jar input.jar output.jar [config.json]
```

### Примеры
```bash
# Базовая обфускация с настройками по умолчанию
java -jar fabric-obfuscator.jar mymod.jar mymod-obfuscated.jar

# С пользовательской конфигурацией
java -jar fabric-obfuscator.jar mymod.jar mymod-obfuscated.jar custom-config.json
```

## ⚙️ Конфигурация

### Уровни обфускации имен:
- **1** - Простые случайные имена (a, b, c...)
- **2** - Unicode символы (α, β, γ...)
- **3** - Комбинированные с невидимыми символами
- **4** - Экстремально сложные Unicode последовательности
- **5** - Максимальная защита с криптографическими хешами

### Уровни шифрования строк:
- **1** - Простое XOR шифрование
- **2** - Двойное XOR с ватермаркой Fix85
- **3** - Многослойное шифрование
- **4** - AES шифрование с максимальной защитой

### Пример конфигурации:
```json
{
  "nameObfuscationLevel": 5,
  "stringObfuscationLevel": 4,
  "enableWatermarks": true,
  "watermarkConfig": {
    "author": "Fix85",
    "embedInClassNames": true,
    "embedInMethodNames": true,
    "embedInConstants": true,
    "watermarkDensity": 5
  },
  "protectionConfig": {
    "antiDecompilation": true,
    "antiReflection": true,
    "integrityChecks": true,
    "protectionLevel": 5
  }
}
```

## 🔍 Система ватермарок

Обфускатор автоматически встраивает ватермарки **Fix85** в код:

- **Скрытые поля** с зашифрованными подписями
- **Фиктивные методы** с ватермарками в именах
- **Константы** с информацией об авторе
- **Временные метки** обфускации
- **Проверки целостности** ватермарок

## 🛠️ Архитектура

```
src/main/java/com/fabricobfuscator/
├── FabricObfuscator.java          # Главный класс
├── core/
│   └── ObfuscationEngine.java     # Движок обфускации
├── transformers/
│   ├── NameObfuscator.java        # Обфускация имен
│   └── StringObfuscator.java      # Шифрование строк
├── protection/
│   └── WatermarkSystem.java       # Система ватермарок Fix85
├── crypto/
│   └── CryptoUtils.java           # Криптографические утилиты
└── config/
    └── ObfuscationConfig.java     # Конфигурация
```

## 🔒 Безопасность

### Что защищает обфускатор:
- ✅ Имена классов, методов и полей
- ✅ Строковые литералы
- ✅ Константы и числовые значения
- ✅ Структура кода
- ✅ Логика выполнения

### Что НЕ затрагивается:
- ❌ Minecraft API вызовы
- ❌ Fabric API интеграция
- ❌ Mixin классы
- ❌ Функциональность мода

## 📊 Результаты

После обфускации Fix85:
- **99%** имен становятся нечитаемыми
- **100%** строк зашифрованы
- **Множественные** ватермарки встроены
- **Полная** совместимость с Fabric
- **Невозможность** восстановления оригинального кода

## 🎯 Совместимость

- ✅ Все версии Minecraft
- ✅ Все версии Fabric
- ✅ Fabric API
- ✅ Mixin Framework
- ✅ Quilt (частично)

## 📝 Лицензия

© 2024 **Fix85** - Все права защищены

Этот обфускатор создан **Fix85** и содержит встроенные ватермарки автора.

## 🤝 Поддержка

Для вопросов и поддержки обращайтесь к **Fix85**.

---

**⚠️ Важно:** Обфускация необратима! Всегда сохраняйте оригинальные исходники.

**🔥 Fix85 Obfuscator - Максимальная защита для ваших Fabric модов!**
