package com.example.testmod;

import net.fabricmc.api.ModInitializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Тестовый Fabric мод для демонстрации обфускации Fix85
 * Этот мод будет обфусцирован с максимальной защитой
 */
public class TestMod implements ModInitializer {
    public static final String MOD_ID = "testmod";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);
    
    // Секретные данные для защиты
    private static final String SECRET_KEY = "MySecretKey123";
    private static final String API_TOKEN = "abc123def456ghi789";
    private static final int MAGIC_NUMBER = 42;
    
    @Override
    public void onInitialize() {
        LOGGER.info("Инициализация тестового мода");
        
        // Вызываем различные методы для демонстрации
        performSecretOperation();
        calculateImportantValue();
        processUserData("TestUser", "password123");
        
        LOGGER.info("Тестовый мод успешно загружен!");
    }
    
    /**
     * Секретная операция, которую нужно защитить
     */
    private void performSecretOperation() {
        String encryptedData = encryptString(SECRET_KEY);
        LOGGER.debug("Выполнена секретная операция: {}", encryptedData);
        
        // Проверка лицензии
        if (validateLicense(API_TOKEN)) {
            LOGGER.info("Лицензия действительна");
        } else {
            LOGGER.warn("Недействительная лицензия!");
        }
    }
    
    /**
     * Вычисление важного значения
     */
    private int calculateImportantValue() {
        int result = MAGIC_NUMBER * 2 + 8;
        
        // Сложная логика для защиты
        for (int i = 0; i < 10; i++) {
            result = (result ^ i) + MAGIC_NUMBER;
        }
        
        LOGGER.debug("Вычислено важное значение: {}", result);
        return result;
    }
    
    /**
     * Обработка пользовательских данных
     */
    private void processUserData(String username, String password) {
        // Хеширование пароля
        String hashedPassword = hashPassword(password);
        
        // Создание пользовательской сессии
        UserSession session = new UserSession(username, hashedPassword);
        session.initialize();
        
        LOGGER.info("Обработаны данные пользователя: {}", username);
    }
    
    /**
     * Простое шифрование строки
     */
    private String encryptString(String input) {
        StringBuilder encrypted = new StringBuilder();
        
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            encrypted.append((char) (c + MAGIC_NUMBER));
        }
        
        return encrypted.toString();
    }
    
    /**
     * Проверка лицензии
     */
    private boolean validateLicense(String token) {
        // Простая проверка длины токена
        if (token == null || token.length() < 10) {
            return false;
        }
        
        // Проверка контрольной суммы
        int checksum = 0;
        for (char c : token.toCharArray()) {
            checksum += c;
        }
        
        return checksum % MAGIC_NUMBER == 0;
    }
    
    /**
     * Хеширование пароля
     */
    private String hashPassword(String password) {
        int hash = password.hashCode();
        return Integer.toHexString(hash ^ MAGIC_NUMBER);
    }
    
    /**
     * Внутренний класс для пользовательской сессии
     */
    private static class UserSession {
        private final String username;
        private final String passwordHash;
        private boolean initialized = false;
        
        public UserSession(String username, String passwordHash) {
            this.username = username;
            this.passwordHash = passwordHash;
        }
        
        public void initialize() {
            // Инициализация сессии
            this.initialized = true;
            LOGGER.debug("Сессия инициализирована для пользователя: {}", username);
        }
        
        public boolean isValid() {
            return initialized && username != null && passwordHash != null;
        }
        
        public String getUsername() {
            return username;
        }
    }
    
    /**
     * Статический блок инициализации
     */
    static {
        LOGGER.info("Статическая инициализация TestMod");
        
        // Проверка системных требований
        String javaVersion = System.getProperty("java.version");
        LOGGER.debug("Версия Java: {}", javaVersion);
        
        // Инициализация внутренних структур
        initializeInternalStructures();
    }
    
    /**
     * Инициализация внутренних структур
     */
    private static void initializeInternalStructures() {
        // Создание внутренних объектов
        InternalConfig config = new InternalConfig();
        config.setDebugMode(false);
        config.setMaxUsers(100);
        
        LOGGER.debug("Внутренние структуры инициализированы");
    }
    
    /**
     * Внутренний класс конфигурации
     */
    private static class InternalConfig {
        private boolean debugMode;
        private int maxUsers;
        
        public void setDebugMode(boolean debugMode) {
            this.debugMode = debugMode;
        }
        
        public void setMaxUsers(int maxUsers) {
            this.maxUsers = maxUsers;
        }
        
        public boolean isDebugMode() {
            return debugMode;
        }
        
        public int getMaxUsers() {
            return maxUsers;
        }
    }
}
