package com.fabricobfuscator.core;

import com.fabricobfuscator.config.ObfuscationConfig;
import com.fabricobfuscator.protection.WatermarkSystem;
import com.fabricobfuscator.protection.AntiDebugProtection;
import com.fabricobfuscator.protection.ChecksumProtection;
import com.fabricobfuscator.transformers.NameObfuscator;
import com.fabricobfuscator.transformers.StringObfuscator;
import org.objectweb.asm.ClassReader;
import org.objectweb.asm.ClassWriter;
import org.objectweb.asm.tree.ClassNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.jar.JarOutputStream;

/**
 * Основной движок обфускации Fix85
 * Координирует все этапы обфускации с встроенными ватермарками
 */
public class ObfuscationEngine {
    private static final Logger LOGGER = LoggerFactory.getLogger(ObfuscationEngine.class);
    
    private final ObfuscationConfig config;
    private final NameObfuscator nameObfuscator;
    private final StringObfuscator stringObfuscator;
    private final WatermarkSystem watermarkSystem;
    private final AntiDebugProtection antiDebugProtection;
    private final ChecksumProtection checksumProtection;
    
    private final Map<String, byte[]> processedClasses = new HashMap<>();
    private final Map<String, byte[]> resources = new HashMap<>();
    
    public ObfuscationEngine(ObfuscationConfig config) {
        this.config = config;
        this.nameObfuscator = new NameObfuscator(config);
        this.stringObfuscator = new StringObfuscator(config);
        this.watermarkSystem = new WatermarkSystem();
        this.antiDebugProtection = new AntiDebugProtection();
        this.checksumProtection = new ChecksumProtection();

        LOGGER.info("Инициализирован движок обфускации Fix85");
        LOGGER.info("Конфигурация: {}", config);
    }
    
    /**
     * Выполняет полную обфускацию JAR файла
     */
    public void obfuscate(Path inputPath, Path outputPath) throws IOException {
        LOGGER.info("Начинаем обфускацию: {} -> {}", inputPath, outputPath);
        
        // Этап 1: Загрузка и анализ
        loadJarFile(inputPath);
        LOGGER.info("Загружено {} классов и {} ресурсов", processedClasses.size(), resources.size());
        
        // Этап 2: Обфускация классов
        obfuscateClasses();
        LOGGER.info("Обфускация классов завершена");
        
        // Этап 3: Встраивание ватермарок Fix85
        if (config.isEnableWatermarks()) {
            embedWatermarks();
            LOGGER.info("Ватермарки Fix85 встроены");
        }
        
        // Этап 4: Сохранение результата
        saveJarFile(outputPath);
        LOGGER.info("Обфусцированный JAR сохранен: {}", outputPath);
        
        // Этап 5: Отчет
        generateReport();
    }
    
    /**
     * Загружает JAR файл
     */
    private void loadJarFile(Path jarPath) throws IOException {
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            jarFile.stream().forEach(entry -> {
                try {
                    byte[] data = readJarEntry(jarFile, entry);
                    
                    if (entry.getName().endsWith(".class")) {
                        processedClasses.put(entry.getName(), data);
                    } else {
                        resources.put(entry.getName(), data);
                    }
                } catch (IOException e) {
                    LOGGER.error("Ошибка чтения записи JAR: {}", entry.getName(), e);
                }
            });
        }
    }
    
    /**
     * Читает запись из JAR файла
     */
    private byte[] readJarEntry(JarFile jarFile, JarEntry entry) throws IOException {
        try (InputStream is = jarFile.getInputStream(entry);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            
            return baos.toByteArray();
        }
    }
    
    /**
     * Обфусцирует все классы
     */
    private void obfuscateClasses() {
        Map<String, byte[]> obfuscatedClasses = new HashMap<>();
        
        for (Map.Entry<String, byte[]> entry : processedClasses.entrySet()) {
            String className = entry.getKey();
            byte[] classData = entry.getValue();
            
            try {
                // Пропускаем исключенные классы
                String internalName = className.replace(".class", "");
                if (config.shouldExcludeClass(internalName)) {
                    LOGGER.debug("Пропускаем класс: {}", className);
                    obfuscatedClasses.put(className, classData);
                    continue;
                }
                
                // Обфусцируем класс
                byte[] obfuscatedData = obfuscateClass(classData);
                obfuscatedClasses.put(className, obfuscatedData);
                
                LOGGER.debug("Обфусцирован класс: {}", className);
                
            } catch (Exception e) {
                LOGGER.error("Ошибка обфускации класса: {}", className, e);
                // В случае ошибки сохраняем оригинальный класс
                obfuscatedClasses.put(className, classData);
            }
        }
        
        processedClasses.clear();
        processedClasses.putAll(obfuscatedClasses);
    }
    
    /**
     * Обфусцирует отдельный класс
     */
    private byte[] obfuscateClass(byte[] classData) {
        // Парсим класс
        ClassReader reader = new ClassReader(classData);
        ClassNode classNode = new ClassNode();
        reader.accept(classNode, 0);
        
        // Применяем трансформации
        
        // 1. Обфускация имен
        applyNameObfuscation(classNode);
        
        // 2. Обфускация строк
        if (config.getStringObfuscationLevel() > 0) {
            stringObfuscator.obfuscateStrings(classNode);
        }
        
        // 3. Встраивание ватермарок Fix85
        if (config.isEnableWatermarks()) {
            watermarkSystem.embedWatermarks(classNode);
        }

        // 4. Защита от отладки
        if (config.isAntiDebugProtection()) {
            antiDebugProtection.addAntiDebugProtection(classNode);
        }

        // 5. Контрольные суммы и проверки целостности
        if (config.isChecksumProtection()) {
            checksumProtection.addChecksumProtection(classNode);
        }
        
        // Генерируем байт-код
        ClassWriter writer = new ClassWriter(ClassWriter.COMPUTE_MAXS | ClassWriter.COMPUTE_FRAMES);
        classNode.accept(writer);
        
        return writer.toByteArray();
    }
    
    /**
     * Применяет обфускацию имен
     */
    private void applyNameObfuscation(ClassNode classNode) {
        if (config.getNameObfuscationLevel() == 0) {
            return;
        }
        
        // Обфусцируем имя класса
        String originalName = classNode.name;
        String obfuscatedName = nameObfuscator.obfuscateClassName(originalName);
        classNode.name = obfuscatedName;
        
        // Обфусцируем методы
        classNode.methods.forEach(method -> {
            if (!config.shouldExcludeMethod(method.name)) {
                String obfuscatedMethodName = nameObfuscator.obfuscateMethodName(
                    classNode.name, method.name, method.desc);
                method.name = obfuscatedMethodName;
            }
        });
        
        // Обфусцируем поля
        classNode.fields.forEach(field -> {
            String obfuscatedFieldName = nameObfuscator.obfuscateFieldName(
                classNode.name, field.name);
            field.name = obfuscatedFieldName;
        });
    }
    
    /**
     * Встраивает дополнительные ватермарки
     */
    private void embedWatermarks() {
        LOGGER.info("Встраиваем ватермарки Fix85 в {} классов", processedClasses.size());
        
        // Добавляем специальный класс с информацией о ватермарке
        addWatermarkInfoClass();
        
        // Встраиваем скрытые ватермарки в случайные классы
        embedHiddenWatermarks();
    }
    
    /**
     * Добавляет класс с информацией о ватермарке
     */
    private void addWatermarkInfoClass() {
        ClassNode watermarkClass = new ClassNode();
        watermarkClass.version = 52; // Java 8
        watermarkClass.access = 1; // public
        watermarkClass.name = "\u200BFix85\u200CWatermark\u200D"; // Скрытое имя
        watermarkClass.superName = "java/lang/Object";
        
        // Встраиваем ватермарки
        watermarkSystem.embedWatermarks(watermarkClass);
        
        // Конвертируем в байт-код
        ClassWriter writer = new ClassWriter(0);
        watermarkClass.accept(writer);
        
        processedClasses.put(watermarkClass.name + ".class", writer.toByteArray());
    }
    
    /**
     * Встраивает скрытые ватермарки в случайные классы
     */
    private void embedHiddenWatermarks() {
        int watermarkCount = 0;
        int targetCount = Math.min(5, processedClasses.size() / 10); // 10% классов
        
        for (Map.Entry<String, byte[]> entry : processedClasses.entrySet()) {
            if (watermarkCount >= targetCount) {
                break;
            }
            
            try {
                ClassReader reader = new ClassReader(entry.getValue());
                ClassNode classNode = new ClassNode();
                reader.accept(classNode, 0);
                
                // Встраиваем дополнительные ватермарки
                watermarkSystem.embedWatermarks(classNode);
                
                ClassWriter writer = new ClassWriter(ClassWriter.COMPUTE_MAXS);
                classNode.accept(writer);
                
                entry.setValue(writer.toByteArray());
                watermarkCount++;
                
            } catch (Exception e) {
                LOGGER.debug("Не удалось встроить ватермарку в класс: {}", entry.getKey());
            }
        }
        
        LOGGER.info("Встроено {} дополнительных ватермарок", watermarkCount);
    }
    
    /**
     * Сохраняет обфусцированный JAR файл
     */
    private void saveJarFile(Path outputPath) throws IOException {
        try (JarOutputStream jos = new JarOutputStream(Files.newOutputStream(outputPath))) {
            
            // Сохраняем классы
            for (Map.Entry<String, byte[]> entry : processedClasses.entrySet()) {
                JarEntry jarEntry = new JarEntry(entry.getKey());
                jos.putNextEntry(jarEntry);
                jos.write(entry.getValue());
                jos.closeEntry();
            }
            
            // Сохраняем ресурсы
            for (Map.Entry<String, byte[]> entry : resources.entrySet()) {
                JarEntry jarEntry = new JarEntry(entry.getKey());
                jos.putNextEntry(jarEntry);
                jos.write(entry.getValue());
                jos.closeEntry();
            }
        }
    }
    
    /**
     * Генерирует отчет об обфускации
     */
    private void generateReport() {
        LOGGER.info("=== ОТЧЕТ ОБ ОБФУСКАЦИИ Fix85 ===");
        LOGGER.info("Обработано классов: {}", processedClasses.size());
        LOGGER.info("Обработано ресурсов: {}", resources.size());
        LOGGER.info("Уровень обфускации имен: {}", config.getNameObfuscationLevel());
        LOGGER.info("Уровень обфускации строк: {}", config.getStringObfuscationLevel());
        LOGGER.info("Ватермарки Fix85: {}", config.isEnableWatermarks() ? "ВКЛЮЧЕНЫ" : "ОТКЛЮЧЕНЫ");
        LOGGER.info("Автор: {}", config.getWatermarkConfig().getAuthor());
        LOGGER.info("================================");
    }
}
