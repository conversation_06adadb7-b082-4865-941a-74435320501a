package com.fabricobfuscator.transformers;

import com.fabricobfuscator.config.ObfuscationConfig;
import com.fabricobfuscator.utils.CryptoUtils;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.FieldNode;
import org.objectweb.asm.tree.MethodNode;

import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Продвинутый обфускатор имен с использованием множественных алгоритмов
 * Создает практически невосстановимые имена классов, методов и полей
 */
public class NameObfuscator {
    private final ObfuscationConfig config;
    private final SecureRandom random;
    private final Map<String, String> classNameMap = new ConcurrentHashMap<>();
    private final Map<String, String> methodNameMap = new ConcurrentHashMap<>();
    private final Map<String, String> fieldNameMap = new ConcurrentHashMap<>();
    
    // Наборы символов для генерации имен
    private static final String[] UNICODE_RANGES = {
        // Невидимые символы
        "\u200B\u200C\u200D\u2060\uFEFF",
        // Похожие символы
        "Il1|oO0",
        // Математические символы
        "αβγδεζηθικλμνξοπρστυφχψω",
        // Специальные символы
        "ǀǁǂǃǄǅǆǇǈǉǊǋǌǍǎǏ"
    };
    
    private static final Set<String> RESERVED_NAMES = Set.of(
        "main", "init", "<init>", "<clinit>", "toString", "equals", "hashCode",
        "clone", "finalize", "getClass", "notify", "notifyAll", "wait"
    );
    
    public NameObfuscator(ObfuscationConfig config) {
        this.config = config;
        this.random = new SecureRandom();
    }
    
    /**
     * Обфусцирует имя класса с использованием многоуровневого алгоритма
     */
    public String obfuscateClassName(String originalName) {
        if (shouldSkipClass(originalName)) {
            return originalName;
        }
        
        return classNameMap.computeIfAbsent(originalName, name -> {
            switch (config.getNameObfuscationLevel()) {
                case 1: return generateSimpleName();
                case 2: return generateUnicodeName();
                case 3: return generateAdvancedName();
                case 4: return generateExtremelyObfuscatedName();
                default: return generateMaximallyObfuscatedName(name);
            }
        });
    }
    
    /**
     * Обфусцирует имя метода
     */
    public String obfuscateMethodName(String className, String methodName, String descriptor) {
        String key = className + "." + methodName + descriptor;
        
        if (shouldSkipMethod(methodName)) {
            return methodName;
        }
        
        return methodNameMap.computeIfAbsent(key, k -> {
            switch (config.getNameObfuscationLevel()) {
                case 1: return generateSimpleName();
                case 2: return generateUnicodeName();
                case 3: return generateAdvancedName();
                case 4: return generateExtremelyObfuscatedName();
                default: return generateMaximallyObfuscatedName(methodName);
            }
        });
    }
    
    /**
     * Обфусцирует имя поля
     */
    public String obfuscateFieldName(String className, String fieldName) {
        String key = className + "." + fieldName;
        
        return fieldNameMap.computeIfAbsent(key, k -> {
            switch (config.getNameObfuscationLevel()) {
                case 1: return generateSimpleName();
                case 2: return generateUnicodeName();
                case 3: return generateAdvancedName();
                case 4: return generateExtremelyObfuscatedName();
                default: return generateMaximallyObfuscatedName(fieldName);
            }
        });
    }
    
    private boolean shouldSkipClass(String className) {
        // Пропускаем системные классы и Fabric API
        return className.startsWith("java/") ||
               className.startsWith("javax/") ||
               className.startsWith("net/minecraft/") ||
               className.startsWith("net/fabricmc/") ||
               className.startsWith("org/spongepowered/") ||
               className.contains("mixin") ||
               className.contains("Mixin");
    }
    
    private boolean shouldSkipMethod(String methodName) {
        return RESERVED_NAMES.contains(methodName) ||
               methodName.startsWith("lambda$") ||
               methodName.contains("$");
    }
    
    /**
     * Генерирует простое обфусцированное имя (уровень 1)
     */
    private String generateSimpleName() {
        StringBuilder sb = new StringBuilder();
        int length = 3 + random.nextInt(5);
        
        for (int i = 0; i < length; i++) {
            if (i == 0) {
                sb.append((char) ('a' + random.nextInt(26)));
            } else {
                if (random.nextBoolean()) {
                    sb.append((char) ('a' + random.nextInt(26)));
                } else {
                    sb.append((char) ('0' + random.nextInt(10)));
                }
            }
        }
        
        return sb.toString();
    }
    
    /**
     * Генерирует имя с Unicode символами (уровень 2)
     */
    private String generateUnicodeName() {
        StringBuilder sb = new StringBuilder();
        int length = 2 + random.nextInt(4);
        
        for (int i = 0; i < length; i++) {
            String range = UNICODE_RANGES[random.nextInt(UNICODE_RANGES.length)];
            sb.append(range.charAt(random.nextInt(range.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * Генерирует продвинутое обфусцированное имя (уровень 3)
     */
    private String generateAdvancedName() {
        StringBuilder sb = new StringBuilder();
        
        // Комбинируем различные типы символов
        sb.append(generateUnicodeName());
        sb.append(generateSimpleName());
        
        // Добавляем невидимые символы для усложнения
        String invisible = UNICODE_RANGES[0];
        for (int i = 0; i < 2 + random.nextInt(3); i++) {
            int pos = random.nextInt(sb.length() + 1);
            sb.insert(pos, invisible.charAt(random.nextInt(invisible.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * Генерирует крайне обфусцированное имя (уровень 4)
     */
    private String generateExtremelyObfuscatedName() {
        StringBuilder sb = new StringBuilder();
        
        // Создаем базовое имя из различных символов
        for (int i = 0; i < 5 + random.nextInt(10); i++) {
            String range = UNICODE_RANGES[random.nextInt(UNICODE_RANGES.length)];
            sb.append(range.charAt(random.nextInt(range.length())));
        }
        
        // Добавляем множество невидимых символов
        String invisible = UNICODE_RANGES[0];
        for (int i = 0; i < 10 + random.nextInt(20); i++) {
            int pos = random.nextInt(sb.length() + 1);
            sb.insert(pos, invisible.charAt(random.nextInt(invisible.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * Генерирует максимально обфусцированное имя (уровень 5+)
     */
    private String generateMaximallyObfuscatedName(String originalName) {
        // Используем криптографический хеш от оригинального имени
        byte[] hash = CryptoUtils.sha256(originalName.getBytes());
        
        StringBuilder sb = new StringBuilder();
        
        // Преобразуем хеш в Unicode символы
        for (int i = 0; i < Math.min(hash.length, 16); i++) {
            int value = Math.abs(hash[i]) % UNICODE_RANGES.length;
            String range = UNICODE_RANGES[value];
            int charIndex = Math.abs(hash[(i + 1) % hash.length]) % range.length();
            sb.append(range.charAt(charIndex));
        }
        
        // Добавляем случайные невидимые символы
        String invisible = UNICODE_RANGES[0];
        for (int i = 0; i < 20 + random.nextInt(30); i++) {
            int pos = random.nextInt(sb.length() + 1);
            char invisibleChar = invisible.charAt(hash[i % hash.length] & 0xFF % invisible.length());
            sb.insert(pos, invisibleChar);
        }
        
        // Добавляем временную метку для уникальности
        long timestamp = System.nanoTime();
        for (int i = 0; i < 8; i++) {
            int value = (int) ((timestamp >> (i * 8)) & 0xFF);
            String range = UNICODE_RANGES[value % UNICODE_RANGES.length];
            sb.append(range.charAt(value % range.length()));
        }
        
        return sb.toString();
    }
    
    public Map<String, String> getClassNameMap() {
        return new HashMap<>(classNameMap);
    }
    
    public Map<String, String> getMethodNameMap() {
        return new HashMap<>(methodNameMap);
    }
    
    public Map<String, String> getFieldNameMap() {
        return new HashMap<>(fieldNameMap);
    }
}
