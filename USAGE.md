# Fix85 Fabric Obfuscator - Руководство по использованию

## 🚀 Быстрый старт

### 1. Сборка обфускатора
```bash
# Windows
build.bat

# Linux/Mac
chmod +x build.sh
./build.sh
```

### 2. Базовое использование
```bash
java -jar build/libs/fabric-obfuscator-1.0.0.jar input.jar output.jar
```

### 3. С пользовательской конфигурацией
```bash
java -jar build/libs/fabric-obfuscator-1.0.0.jar input.jar output.jar config.json
```

## ⚙️ Конфигурация

### Структура конфигурационного файла:
```json
{
  "nameObfuscationLevel": 5,
  "stringObfuscationLevel": 4,
  "antiDebugProtection": true,
  "checksumProtection": true,
  "enableWatermarks": true,
  "excludeClasses": [
    "net/minecraft/**",
    "net/fabricmc/**"
  ],
  "watermarkConfig": {
    "author": "Fix85",
    "embedInClassNames": true,
    "embedInMethodNames": true,
    "embedInConstants": true,
    "watermarkDensity": 5
  },
  "protectionConfig": {
    "antiDecompilation": true,
    "antiReflection": true,
    "integrityChecks": true,
    "protectionLevel": 5
  }
}
```

## 🔒 Уровни защиты

### Обфускация имен (1-5):
- **Уровень 1**: Простые случайные имена (a, b, c...)
- **Уровень 2**: Unicode символы (α, β, γ...)
- **Уровень 3**: Комбинированные с невидимыми символами
- **Уровень 4**: Экстремально сложные Unicode последовательности
- **Уровень 5**: Максимальная защита с криптографическими хешами

### Шифрование строк (1-4):
- **Уровень 1**: Простое XOR шифрование
- **Уровень 2**: Двойное XOR с ватермаркой Fix85
- **Уровень 3**: Многослойное шифрование
- **Уровень 4**: AES шифрование с максимальной защитой

## 🛡️ Системы защиты

### 1. Ватермарки Fix85
- Скрытые метки в именах классов и методов
- Зашифрованные константы с подписью автора
- Временные метки обфускации
- Невидимые Unicode символы
- Проверка целостности ватермарок

### 2. Антиотладочная защита
- Обнаружение отладчиков (jdwp, debugger)
- Проверки времени выполнения
- Анализ среды выполнения
- Проверки системных свойств
- Защитные реакции при обнаружении

### 3. Контрольные суммы
- SHA-256 хеши классов и методов
- Зашифрованное хранение контрольных сумм
- Проверки целостности во время выполнения
- Самопроверка кода
- Защита от модификации

## 📊 Примеры использования

### Максимальная защита:
```json
{
  "nameObfuscationLevel": 5,
  "stringObfuscationLevel": 4,
  "antiDebugProtection": true,
  "checksumProtection": true,
  "enableWatermarks": true,
  "watermarkConfig": {
    "author": "Fix85",
    "watermarkDensity": 10
  },
  "protectionConfig": {
    "protectionLevel": 5
  }
}
```

### Быстрая обфускация:
```json
{
  "nameObfuscationLevel": 2,
  "stringObfuscationLevel": 1,
  "antiDebugProtection": false,
  "checksumProtection": false,
  "enableWatermarks": true
}
```

### Только ватермарки:
```json
{
  "nameObfuscationLevel": 0,
  "stringObfuscationLevel": 0,
  "antiDebugProtection": false,
  "checksumProtection": false,
  "enableWatermarks": true,
  "watermarkConfig": {
    "author": "Fix85",
    "embedInClassNames": true,
    "embedInMethodNames": true,
    "embedInConstants": true
  }
}
```

## 🎯 Специфика для Fabric

### Автоматические исключения:
- `net/minecraft/**` - Minecraft API
- `net/fabricmc/**` - Fabric API
- `org/spongepowered/**` - Mixin Framework
- `**/*Mixin*` - Mixin классы
- `**/*mixin*` - Mixin классы (lowercase)

### Сохраняемые методы:
- `main` - Точки входа
- `init` - Инициализация
- `onInitialize*` - Fabric инициализация
- `on*` - Event handlers

## 🔧 Расширенные настройки

### Исключения классов:
```json
{
  "excludeClasses": [
    "com/mymod/api/**",
    "com/mymod/compat/**",
    "**/*Config*"
  ]
}
```

### Исключения методов:
```json
{
  "excludeMethods": [
    "toString",
    "equals",
    "hashCode",
    "get*",
    "set*"
  ]
}
```

### Плотность ватермарок:
```json
{
  "watermarkConfig": {
    "watermarkDensity": 10,
    "embedTimestamp": true,
    "embedInClassNames": true,
    "embedInMethodNames": true,
    "embedInConstants": true
  }
}
```

## 🚨 Важные замечания

### ⚠️ Предупреждения:
1. **Обфускация необратима** - всегда сохраняйте оригинальные исходники
2. **Тестируйте мод** после обфускации на совместимость
3. **Производительность** может снизиться при максимальных настройках
4. **Отладка** обфусцированного кода крайне затруднена

### ✅ Рекомендации:
1. Используйте уровень 3-4 для продакшена
2. Тестируйте на разных версиях Minecraft
3. Проверяйте совместимость с другими модами
4. Делайте резервные копии перед обфускацией

## 🔍 Анализ результатов

### Проверка обфускации:
```bash
# Декомпиляция для проверки
java -jar fernflower.jar obfuscated.jar output/

# Анализ строк
strings obfuscated.jar | grep -i "fix85"

# Проверка размера
ls -la original.jar obfuscated.jar
```

### Ожидаемые результаты:
- Имена классов/методов нечитаемы
- Строки зашифрованы
- Ватермарки Fix85 встроены
- Размер файла увеличен на 10-30%
- Время загрузки увеличено незначительно

## 📞 Поддержка

Для вопросов и поддержки обращайтесь к **Fix85**.

---

**© 2024 Fix85 - Максимальная защита для ваших Fabric модов!**
