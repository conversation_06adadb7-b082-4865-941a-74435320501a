@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Fix85 Fabric Obfuscator                  ║
echo ║                   Ручная сборка проекта                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/6] Проверка Java...
java -version
if %ERRORLEVEL% neq 0 (
    echo ❌ Java не найдена! Установите Java 17 или выше.
    pause
    exit /b 1
)

echo.
echo [2/6] Создание директорий...
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes
if not exist "build\libs" mkdir build\libs
if not exist "lib" mkdir lib

echo.
echo [3/6] Скачивание зависимостей...
echo Скачиваем ASM...
if not exist "lib\asm-9.6.jar" (
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/ow2/asm/asm/9.6/asm-9.6.jar' -OutFile 'lib\asm-9.6.jar'"
)

if not exist "lib\asm-tree-9.6.jar" (
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/ow2/asm/asm-tree/9.6/asm-tree-9.6.jar' -OutFile 'lib\asm-tree-9.6.jar'"
)

if not exist "lib\asm-commons-9.6.jar" (
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/ow2/asm/asm-commons/9.6/asm-commons-9.6.jar' -OutFile 'lib\asm-commons-9.6.jar'"
)

if not exist "lib\gson-2.10.1.jar" (
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar' -OutFile 'lib\gson-2.10.1.jar'"
)

echo.
echo [4/6] Компиляция исходного кода...
set CLASSPATH=lib\asm-9.6.jar;lib\asm-tree-9.6.jar;lib\asm-commons-9.6.jar;lib\gson-2.10.1.jar

echo Компилируем основные классы...
javac -cp "%CLASSPATH%" -d build\classes src\main\java\com\fabricobfuscator\*.java
javac -cp "%CLASSPATH%;build\classes" -d build\classes src\main\java\com\fabricobfuscator\core\*.java
javac -cp "%CLASSPATH%;build\classes" -d build\classes src\main\java\com\fabricobfuscator\config\*.java
javac -cp "%CLASSPATH%;build\classes" -d build\classes src\main\java\com\fabricobfuscator\crypto\*.java
javac -cp "%CLASSPATH%;build\classes" -d build\classes src\main\java\com\fabricobfuscator\transformers\*.java
javac -cp "%CLASSPATH%;build\classes" -d build\classes src\main\java\com\fabricobfuscator\protection\*.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Ошибка компиляции!
    pause
    exit /b 1
)

echo.
echo [5/6] Копирование ресурсов...
if exist "src\main\resources" (
    xcopy /E /Y src\main\resources build\classes\
)

echo.
echo [6/6] Создание JAR файла...
echo Main-Class: com.fabricobfuscator.FabricObfuscator > build\MANIFEST.MF
echo Class-Path: asm-9.6.jar asm-tree-9.6.jar asm-commons-9.6.jar gson-2.10.1.jar >> build\MANIFEST.MF
echo. >> build\MANIFEST.MF

cd build\classes
jar cfm ..\libs\fabric-obfuscator-1.0.0.jar ..\MANIFEST.MF *
cd ..\..

echo.
echo [7/6] Копирование зависимостей...
copy lib\*.jar build\libs\

echo.
echo ✅ Сборка завершена успешно!
echo 📦 Исполняемый файл: build\libs\fabric-obfuscator-1.0.0.jar
echo 📚 Зависимости скопированы в: build\libs\
echo.
echo Использование:
echo   cd build\libs
echo   java -jar fabric-obfuscator-1.0.0.jar input.jar output.jar
echo.
echo © 2024 Fix85 - Все права защищены
pause
