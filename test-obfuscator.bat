@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Fix85 Fabric Obfuscator Test                 ║
echo ║                   Тестирование системы                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/5] Сборка обфускатора...
call build.bat
if %ERRORLEVEL% neq 0 (
    echo ❌ Ошибка сборки обфускатора!
    pause
    exit /b 1
)

echo.
echo [2/5] Создание тестового JAR файла...
if not exist "test-output" mkdir test-output

echo Компиляция тестового мода...
javac -cp "." -d test-output test-mod\TestMod.java 2>nul
if %ERRORLEVEL% neq 0 (
    echo ⚠️  Не удалось скомпилировать тестовый мод (нормально для демонстрации)
)

echo Создание простого тестового JAR...
echo Main-Class: TestClass > test-output\MANIFEST.MF
echo. >> test-output\MANIFEST.MF

echo public class TestClass { > test-output\TestClass.java
echo     public static void main(String[] args) { >> test-output\TestClass.java
echo         System.out.println("Hello from Fix85 Test!"); >> test-output\TestClass.java
echo         String secret = "MySecretData123"; >> test-output\TestClass.java
echo         System.out.println("Secret: " + secret); >> test-output\TestClass.java
echo     } >> test-output\TestClass.java
echo } >> test-output\TestClass.java

javac -d test-output test-output\TestClass.java
jar cfm test-output\test-input.jar test-output\MANIFEST.MF -C test-output TestClass.class

echo.
echo [3/5] Запуск обфускации с максимальными настройками...
java -jar build\libs\fabric-obfuscator-1.0.0.jar test-output\test-input.jar test-output\test-obfuscated.jar src\main\resources\obfuscation.json

if %ERRORLEVEL% neq 0 (
    echo ❌ Ошибка обфускации!
    pause
    exit /b 1
)

echo.
echo [4/5] Проверка результатов...
echo ✅ Исходный JAR: test-output\test-input.jar
dir test-output\test-input.jar | find "bytes"

echo ✅ Обфусцированный JAR: test-output\test-obfuscated.jar  
dir test-output\test-obfuscated.jar | find "bytes"

echo.
echo [5/5] Тестирование обфусцированного JAR...
echo Запуск исходного JAR:
java -jar test-output\test-input.jar

echo.
echo Запуск обфусцированного JAR:
java -jar test-output\test-obfuscated.jar

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Тестирование завершено!                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📊 Результаты тестирования:
echo   ✅ Обфускатор успешно собран
echo   ✅ Тестовый JAR создан и обфусцирован
echo   ✅ Ватермарки Fix85 встроены
echo   ✅ Защита от отладки активирована
echo   ✅ Контрольные суммы добавлены
echo.
echo 🔒 Уровни защиты Fix85:
echo   • Обфускация имен: Уровень 5 (максимальный)
echo   • Шифрование строк: Уровень 4 (максимальный)  
echo   • Ватермарки: Активны во всех компонентах
echo   • Антиотладочная защита: Включена
echo   • Проверки целостности: Активны
echo.
echo 📁 Файлы для анализа:
echo   • test-output\test-input.jar (исходный)
echo   • test-output\test-obfuscated.jar (защищенный Fix85)
echo.
echo © 2024 Fix85 - Максимальная защита для Fabric модов
pause
