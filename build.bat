@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Fix85 Fabric Obfuscator                  ║
echo ║                        Сборка проекта                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/3] Очистка предыдущих сборок...
if exist "build" rmdir /s /q build

echo.
echo [2/3] Компиляция и сборка JAR...
call build-manual.bat
if %ERRORLEVEL% neq 0 (
    echo ❌ Ошибка сборки!
    pause
    exit /b 1
)

echo.
echo [3/3] Создание исполняемого JAR...
echo ✅ JAR уже создан в build\libs\

echo.
echo ✅ Сборка завершена!
echo 📦 Исполняемый файл: build\libs\fabric-obfuscator-1.0.0.jar
echo.
echo Использование:
echo   java -jar build\libs\fabric-obfuscator-1.0.0.jar input.jar output.jar
echo.
echo © 2024 Fix85 - Все права защищены
pause
