{"nameObfuscationLevel": 5, "stringObfuscationLevel": 4, "antiDebugProtection": true, "checksumProtection": true, "enableWatermarks": true, "excludeClasses": ["net/minecraft/**", "net/fabricmc/**", "org/spongepowered/**", "**/*Mixin*", "**/*mixin*"], "excludeMethods": ["main", "init", "onInitialize*", "on*"], "watermarkConfig": {"author": "Fix85", "embedInClassNames": true, "embedInMethodNames": true, "embedInConstants": true, "embedTimestamp": true, "watermarkDensity": 5}, "protectionConfig": {"antiDecompilation": true, "antiReflection": true, "integrityChecks": true, "runtimeValidation": true, "protectionLevel": 5}}